enum ReviewType {
  product,
  seller,
  buyer,
}

class ReviewModel {
  final String id;
  final String reviewerId;
  final String reviewerName;
  final String? reviewerImage;
  final String targetId; // Product ID or User ID
  final ReviewType type;
  final double rating;
  final String comment;
  final List<String> images;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isVerified;
  final int helpfulCount;
  final int reportCount;
  final Map<String, dynamic> metadata;

  ReviewModel({
    required this.id,
    required this.reviewerId,
    required this.reviewerName,
    this.reviewerImage,
    required this.targetId,
    required this.type,
    required this.rating,
    required this.comment,
    this.images = const [],
    required this.createdAt,
    this.updatedAt,
    this.isVerified = false,
    this.helpfulCount = 0,
    this.reportCount = 0,
    this.metadata = const {},
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] ?? '',
      reviewerId: json['reviewer_id'] ?? '',
      reviewerName: json['reviewer_name'] ?? '',
      reviewerImage: json['reviewer_image'],
      targetId: json['target_id'] ?? '',
      type: ReviewType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'product'),
        orElse: () => ReviewType.product,
      ),
      rating: (json['rating'] ?? 0.0).toDouble(),
      comment: json['comment'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      isVerified: json['is_verified'] ?? false,
      helpfulCount: json['helpful_count'] ?? 0,
      reportCount: json['report_count'] ?? 0,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reviewer_id': reviewerId,
      'reviewer_name': reviewerName,
      'reviewer_image': reviewerImage,
      'target_id': targetId,
      'type': type.toString().split('.').last,
      'rating': rating,
      'comment': comment,
      'images': images,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_verified': isVerified,
      'helpful_count': helpfulCount,
      'report_count': reportCount,
      'metadata': metadata,
    };
  }

  ReviewModel copyWith({
    String? id,
    String? reviewerId,
    String? reviewerName,
    String? reviewerImage,
    String? targetId,
    ReviewType? type,
    double? rating,
    String? comment,
    List<String>? images,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    int? helpfulCount,
    int? reportCount,
    Map<String, dynamic>? metadata,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      reviewerId: reviewerId ?? this.reviewerId,
      reviewerName: reviewerName ?? this.reviewerName,
      reviewerImage: reviewerImage ?? this.reviewerImage,
      targetId: targetId ?? this.targetId,
      type: type ?? this.type,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      images: images ?? this.images,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      reportCount: reportCount ?? this.reportCount,
      metadata: metadata ?? this.metadata,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 30) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  String get ratingText {
    if (rating >= 4.5) return 'ممتاز';
    if (rating >= 3.5) return 'جيد جداً';
    if (rating >= 2.5) return 'جيد';
    if (rating >= 1.5) return 'مقبول';
    return 'ضعيف';
  }

  bool get isPositive => rating >= 3.0;
  bool get isNegative => rating < 2.0;

  @override
  String toString() {
    return 'ReviewModel(id: $id, rating: $rating, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReviewModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class ReviewSummary {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution; // rating -> count
  final List<String> topPositiveKeywords;
  final List<String> topNegativeKeywords;

  ReviewSummary({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    this.topPositiveKeywords = const [],
    this.topNegativeKeywords = const [],
  });

  factory ReviewSummary.fromReviews(List<ReviewModel> reviews) {
    if (reviews.isEmpty) {
      return ReviewSummary(
        averageRating: 0.0,
        totalReviews: 0,
        ratingDistribution: {},
      );
    }

    final totalRating = reviews.fold(0.0, (sum, review) => sum + review.rating);
    final averageRating = totalRating / reviews.length;

    final ratingDistribution = <int, int>{};
    for (final review in reviews) {
      final ratingKey = review.rating.round();
      ratingDistribution[ratingKey] = (ratingDistribution[ratingKey] ?? 0) + 1;
    }

    return ReviewSummary(
      averageRating: averageRating,
      totalReviews: reviews.length,
      ratingDistribution: ratingDistribution,
    );
  }

  String get ratingText {
    if (averageRating >= 4.5) return 'ممتاز';
    if (averageRating >= 3.5) return 'جيد جداً';
    if (averageRating >= 2.5) return 'جيد';
    if (averageRating >= 1.5) return 'مقبول';
    return 'ضعيف';
  }

  double getRatingPercentage(int rating) {
    if (totalReviews == 0) return 0.0;
    return (ratingDistribution[rating] ?? 0) / totalReviews;
  }
}
