import 'package:flutter_test/flutter_test.dart';
import 'package:usale/main.dart';
import 'package:usale/services/language_service.dart';
import 'package:usale/services/auth_service.dart';
import 'package:usale/services/products_service.dart';
import 'package:usale/services/search_service.dart';
import 'package:usale/services/chat_service.dart';
import 'package:usale/services/reviews_service.dart';
import 'package:usale/services/notifications_service.dart';

void main() {
  group('USale App Tests', () {
    late LanguageService languageService;
    late AuthService authService;
    late ProductsService productsService;
    late SearchService searchService;
    late ChatService chatService;
    late ReviewsService reviewsService;
    late NotificationsService notificationsService;

    setUp(() {
      languageService = LanguageService();
      authService = AuthService();
      productsService = ProductsService();
      searchService = SearchService();
      chatService = ChatService();
      reviewsService = ReviewsService();
      notificationsService = NotificationsService();
    });

    testWidgets('App should start and show main page', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(USaleApp(languageService: languageService, authService: authService, productsService: productsService, searchService: searchService, chatService: chatService, reviewsService: reviewsService, notificationsService: notificationsService));

      // Verify that the app starts correctly
      expect(find.text('يو سيل'), findsOneWidget);
      expect(find.text('مرحباً بك في السوق الإلكتروني'), findsOneWidget);
    });

    testWidgets('Bottom navigation should work', (WidgetTester tester) async {
      await tester.pumpWidget(USaleApp(languageService: languageService, authService: authService, productsService: productsService, searchService: searchService, chatService: chatService, reviewsService: reviewsService, notificationsService: notificationsService));

      // Test bottom navigation
      expect(find.text('الرئيسية'), findsOneWidget);
      expect(find.text('البحث'), findsOneWidget);
      expect(find.text('المفضلة'), findsOneWidget);
      expect(find.text('الرسائل'), findsOneWidget);
      expect(find.text('الملف الشخصي'), findsOneWidget);

      // Tap on search tab
      await tester.tap(find.text('البحث'));
      await tester.pumpAndSettle();

      // Verify search page is shown
      expect(find.text('البحث'), findsWidgets);
    });

    testWidgets('Categories should be displayed', (WidgetTester tester) async {
      await tester.pumpWidget(USaleApp(languageService: languageService, authService: authService, productsService: productsService, searchService: searchService, chatService: chatService, reviewsService: reviewsService, notificationsService: notificationsService));

      // Verify categories are shown
      expect(find.text('الفئات'), findsOneWidget);
      expect(find.text('سيارات'), findsOneWidget);
      expect(find.text('عقارات'), findsOneWidget);
      expect(find.text('إلكترونيات'), findsOneWidget);
    });

    testWidgets('Featured products should be displayed', (WidgetTester tester) async {
      await tester.pumpWidget(USaleApp(languageService: languageService, authService: authService, productsService: productsService, searchService: searchService, chatService: chatService, reviewsService: reviewsService, notificationsService: notificationsService));

      // Verify featured products section
      expect(find.text('المنتجات المميزة'), findsOneWidget);
      expect(find.text('عرض الكل'), findsWidgets);
    });
  });

  group('Performance Tests', () {
    testWidgets('App should render within reasonable time', (WidgetTester tester) async {
      final testLanguageService = LanguageService();
      final testAuthService = AuthService();
      final testProductsService = ProductsService();
      final testSearchService = SearchService();
      final testChatService = ChatService();
      final testReviewsService = ReviewsService();
      final testNotificationsService = NotificationsService();
      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(USaleApp(languageService: testLanguageService, authService: testAuthService, productsService: testProductsService, searchService: testSearchService, chatService: testChatService, reviewsService: testReviewsService, notificationsService: testNotificationsService));
      await tester.pumpAndSettle();

      stopwatch.stop();

      // App should render within 3 seconds
      expect(stopwatch.elapsedMilliseconds, lessThan(3000));
    });
  });
}
