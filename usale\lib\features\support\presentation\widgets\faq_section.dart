import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class FAQSection extends StatefulWidget {
  final String searchQuery;

  const FAQSection({
    super.key,
    required this.searchQuery,
  });

  @override
  State<FAQSection> createState() => _FAQSectionState();
}

class _FAQSectionState extends State<FAQSection> {
  final List<FAQItem> _faqItems = [
    FAQItem(
      question: 'كيف يمكنني إنشاء حساب جديد؟',
      answer: 'يمكنك إنشاء حساب جديد من خلال النقر على "إنشاء حساب" في الصفحة الرئيسية، ثم إدخال بياناتك الشخصية والتحقق من البريد الإلكتروني أو رقم الهاتف.',
      category: 'الحساب',
    ),
    FAQItem(
      question: 'كيف يمكنني نشر منتج للبيع؟',
      answer: 'اذهب إلى "إضافة منتج" من القائمة الرئيسية، أضف صور المنتج، اكتب وصفاً مفصلاً، حدد السعر والفئة، ثم انقر على "نشر المنتج".',
      category: 'البيع',
    ),
    FAQItem(
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نقبل الدفع بالبطاقات الائتمانية، مدى، STC Pay، Apple Pay، Google Pay، والدفع عند الاستلام في بعض المناطق.',
      category: 'الدفع',
    ),
    FAQItem(
      question: 'كيف يمكنني تتبع طلبي؟',
      answer: 'يمكنك تتبع طلبك من خلال الذهاب إلى "طلباتي" في حسابك، ستجد رقم التتبع وحالة الطلب الحالية.',
      category: 'الطلبات',
    ),
    FAQItem(
      question: 'ما هي سياسة الإرجاع؟',
      answer: 'يمكن إرجاع المنتجات خلال 14 يوماً من تاريخ الاستلام، بشرط أن تكون في حالتها الأصلية مع العبوة والفاتورة.',
      category: 'الإرجاع',
    ),
    FAQItem(
      question: 'كيف يمكنني الإبلاغ عن مشكلة؟',
      answer: 'يمكنك الإبلاغ عن أي مشكلة من خلال "اتصل بنا" أو "الإبلاغ عن مشكلة" في إعدادات التطبيق، وسيتم الرد عليك خلال 24 ساعة.',
      category: 'الدعم',
    ),
    FAQItem(
      question: 'هل التطبيق آمن للاستخدام؟',
      answer: 'نعم، نستخدم أحدث تقنيات التشفير لحماية بياناتك الشخصية والمالية، ونتبع أفضل معايير الأمان العالمية.',
      category: 'الأمان',
    ),
    FAQItem(
      question: 'كيف يمكنني تغيير كلمة المرور؟',
      answer: 'اذهب إلى الإعدادات > الأمان والخصوصية > تغيير كلمة المرور، أدخل كلمة المرور الحالية والجديدة.',
      category: 'الحساب',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final filteredFAQs = _getFilteredFAQs();

    if (filteredFAQs.isEmpty && widget.searchQuery.isNotEmpty) {
      return _buildNoResultsFound();
    }

    return Column(
      children: filteredFAQs.map((faq) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: _buildFAQItem(faq),
        );
      }).toList(),
    );
  }

  List<FAQItem> _getFilteredFAQs() {
    if (widget.searchQuery.isEmpty) {
      return _faqItems;
    }

    return _faqItems.where((faq) {
      return faq.question.toLowerCase().contains(widget.searchQuery.toLowerCase()) ||
             faq.answer.toLowerCase().contains(widget.searchQuery.toLowerCase()) ||
             faq.category.toLowerCase().contains(widget.searchQuery.toLowerCase());
    }).toList();
  }

  Widget _buildFAQItem(FAQItem faq) {
    return Card(
      child: ExpansionTile(
        title: Text(
          faq.question,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Container(
          margin: const EdgeInsets.only(top: 4),
          child: Text(
            faq.category,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              faq.answer,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsFound() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.textLight,
          ),
          const SizedBox(height: 16),
          const Text(
            'لم نجد نتائج',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم نجد أي أسئلة تحتوي على "${widget.searchQuery}"',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textLight,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Navigate to contact support
            },
            child: const Text('اتصل بالدعم'),
          ),
        ],
      ),
    );
  }
}

class FAQItem {
  final String question;
  final String answer;
  final String category;

  FAQItem({
    required this.question,
    required this.answer,
    required this.category,
  });
}
