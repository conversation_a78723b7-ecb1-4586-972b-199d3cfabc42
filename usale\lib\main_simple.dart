import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Core
import 'core/theme/app_theme.dart';
import 'core/localization/app_localizations.dart';

// Features
import 'features/home/<USER>/pages/main_page.dart';

// Services
import 'services/language_service.dart';
import 'services/auth_service.dart';
import 'services/products_service.dart';
import 'services/search_service.dart';
import 'services/chat_service.dart';
import 'services/reviews_service.dart';
import 'services/notifications_service.dart';
import 'services/payment_service.dart';
import 'services/location_service.dart';
import 'services/analytics_service.dart';
import 'services/security_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  debugPrint('✅ Starting USale App (Simple Version)');
  
  runApp(const USaleAppSimple());
}

class USaleAppSimple extends StatelessWidget {
  const USaleAppSimple({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize services
    final languageService = LanguageService();
    final authService = AuthService();
    final productsService = ProductsService();
    final searchService = SearchService();
    final chatService = ChatService();
    final reviewsService = ReviewsService();
    final notificationsService = NotificationsService();
    final paymentService = PaymentService();
    final locationService = LocationService();
    final analyticsService = AnalyticsService();
    final securityService = SecurityService();

    return MultiProvider(
      providers: [
        ChangeNotifierProvider<LanguageService>.value(value: languageService),
        ChangeNotifierProvider<AuthService>.value(value: authService),
        ChangeNotifierProvider<ProductsService>.value(value: productsService),
        ChangeNotifierProvider<SearchService>.value(value: searchService),
        ChangeNotifierProvider<ChatService>.value(value: chatService),
        ChangeNotifierProvider<ReviewsService>.value(value: reviewsService),
        ChangeNotifierProvider<NotificationsService>.value(value: notificationsService),
        ChangeNotifierProvider<PaymentService>.value(value: paymentService),
        ChangeNotifierProvider<LocationService>.value(value: locationService),
        ChangeNotifierProvider<AnalyticsService>.value(value: analyticsService),
        ChangeNotifierProvider<SecurityService>.value(value: securityService),
      ],
      child: Consumer<LanguageService>(
        builder: (context, languageService, child) {
          return MaterialApp(
            title: 'يوسيل - USale',
            debugShowCheckedModeBanner: false,
            
            // Theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            
            // Localization
            locale: Locale(languageService.currentLanguageCode),
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', 'SA'), // Arabic
              Locale('en', 'US'), // English
            ],
            
            // Home
            home: const MainPage(),
            
            // Routes
            routes: {
              '/main': (context) => const MainPage(),
            },
          );
        },
      ),
    );
  }
}
