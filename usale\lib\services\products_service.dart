import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import '../models/product_model.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class ProductsService extends ChangeNotifier {
  // Singleton pattern
  static final ProductsService _instance = ProductsService._internal();
  factory ProductsService() => _instance;
  ProductsService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  final ImagePicker _imagePicker = ImagePicker();

  List<ProductModel> _products = [];
  List<ProductModel> _myProducts = [];
  bool _isLoading = false;

  // Getters
  List<ProductModel> get products => _products;
  List<ProductModel> get myProducts => _myProducts;
  bool get isLoading => _isLoading;

  // Load all products
  Future<void> loadProducts({
    String? category,
    int? limit,
    int? offset,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      _products = await _databaseService.getProducts(
        category: category,
        limit: limit,
        offset: offset,
      );
      
      // If no products in database, add some mock data
      if (_products.isEmpty) {
        await _addMockProducts();
        _products = await _databaseService.getProducts(
          category: category,
          limit: limit,
          offset: offset,
        );
      }
    } catch (e) {
      debugPrint('Error loading products: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load user's products
  Future<void> loadMyProducts() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      _myProducts = await _databaseService.getProducts(
        sellerId: currentUser.id,
      );
    } catch (e) {
      debugPrint('Error loading my products: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get product by ID
  Future<ProductModel?> getProduct(String id) async {
    try {
      return await _databaseService.getProduct(id);
    } catch (e) {
      debugPrint('Error getting product: $e');
      return null;
    }
  }

  // Add new product
  Future<ProductResult> addProduct({
    required String title,
    required String description,
    required double price,
    required String category,
    required String condition,
    required String location,
    List<XFile>? images,
    List<String>? tags,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return ProductResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Process images
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _processImages(images);
      }

      // Create product
      final product = ProductModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        description: description,
        price: price,
        category: category,
        condition: condition,
        location: location,
        sellerId: currentUser.id,
        sellerName: currentUser.displayName,
        createdAt: DateTime.now(),
        images: imageUrls,
        tags: tags ?? [],
      );

      // Save to database
      await _databaseService.insertProduct(product);

      // Update cache
      _products.insert(0, product);
      _myProducts.insert(0, product);
      notifyListeners();

      return ProductResult.success('تم إضافة المنتج بنجاح', product);
    } catch (e) {
      debugPrint('Error adding product: $e');
      return ProductResult.error('حدث خطأ أثناء إضافة المنتج');
    }
  }

  // Update product
  Future<ProductResult> updateProduct({
    required String productId,
    String? title,
    String? description,
    double? price,
    String? category,
    String? condition,
    String? location,
    List<XFile>? newImages,
    List<String>? existingImages,
    List<String>? tags,
    bool? isSold,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return ProductResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Get existing product
      final existingProduct = await _databaseService.getProduct(productId);
      if (existingProduct == null) {
        return ProductResult.error('المنتج غير موجود');
      }

      // Check ownership
      if (existingProduct.sellerId != currentUser.id) {
        return ProductResult.error('ليس لديك صلاحية لتعديل هذا المنتج');
      }

      // Process images
      List<String> imageUrls = existingImages ?? existingProduct.images;
      if (newImages != null && newImages.isNotEmpty) {
        final newImageUrls = await _processImages(newImages);
        imageUrls.addAll(newImageUrls);
      }

      // Update product
      final updatedProduct = existingProduct.copyWith(
        title: title,
        description: description,
        price: price,
        category: category,
        condition: condition,
        location: location,
        images: imageUrls,
        tags: tags,
        status: isSold == true ? ProductStatus.sold : null,
        updatedAt: DateTime.now(),
      );

      // Save to database
      await _databaseService.updateProduct(updatedProduct);

      // Update cache
      final productIndex = _products.indexWhere((p) => p.id == productId);
      if (productIndex != -1) {
        _products[productIndex] = updatedProduct;
      }

      final myProductIndex = _myProducts.indexWhere((p) => p.id == productId);
      if (myProductIndex != -1) {
        _myProducts[myProductIndex] = updatedProduct;
      }

      notifyListeners();

      return ProductResult.success('تم تحديث المنتج بنجاح', updatedProduct);
    } catch (e) {
      debugPrint('Error updating product: $e');
      return ProductResult.error('حدث خطأ أثناء تحديث المنتج');
    }
  }

  // Delete product
  Future<ProductResult> deleteProduct(String productId) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return ProductResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Get existing product
      final existingProduct = await _databaseService.getProduct(productId);
      if (existingProduct == null) {
        return ProductResult.error('المنتج غير موجود');
      }

      // Check ownership
      if (existingProduct.sellerId != currentUser.id) {
        return ProductResult.error('ليس لديك صلاحية لحذف هذا المنتج');
      }

      // Delete from database
      await _databaseService.deleteProduct(productId);

      // Update cache
      _products.removeWhere((p) => p.id == productId);
      _myProducts.removeWhere((p) => p.id == productId);
      notifyListeners();

      return ProductResult.success('تم حذف المنتج بنجاح');
    } catch (e) {
      debugPrint('Error deleting product: $e');
      return ProductResult.error('حدث خطأ أثناء حذف المنتج');
    }
  }

  // Search products
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      return await _databaseService.searchProducts(query);
    } catch (e) {
      debugPrint('Error searching products: $e');
      return [];
    }
  }

  // Get featured products
  List<ProductModel> getFeaturedProducts() {
    return _products.where((product) => product.isFeatured).toList();
  }

  // Get recent products
  List<ProductModel> getRecentProducts({int limit = 10}) {
    final sorted = List<ProductModel>.from(_products);
    sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sorted.take(limit).toList();
  }

  // Get products by category
  List<ProductModel> getProductsByCategory(String category) {
    return _products.where((product) => product.category == category).toList();
  }

  // Pick images
  Future<List<XFile>> pickImages({bool multiple = true}) async {
    try {
      if (multiple) {
        return await _imagePicker.pickMultiImage();
      } else {
        final image = await _imagePicker.pickImage(source: ImageSource.gallery);
        return image != null ? [image] : [];
      }
    } catch (e) {
      debugPrint('Error picking images: $e');
      return [];
    }
  }

  // Process images (in real app, upload to server)
  Future<List<String>> _processImages(List<XFile> images) async {
    List<String> imageUrls = [];
    
    for (final image in images) {
      // In real app, upload to server and get URL
      // For now, use local path
      imageUrls.add(image.path);
    }
    
    return imageUrls;
  }

  // Add mock products for testing
  Future<void> _addMockProducts() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    final mockProducts = [
      ProductModel(
        id: '1',
        title: 'آيفون 15 برو ماكس 256 جيجا',
        description: 'جهاز جديد بالكرتون، لون تيتانيوم طبيعي، ضمان سنة كاملة',
        price: 4500,
        category: 'إلكترونيات',
        condition: 'جديد',
        location: 'الرياض',
        sellerId: currentUser.id,
        sellerName: currentUser.displayName,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        isFeatured: true,
        images: [],
        tags: ['آيفون', 'أبل', 'جوال'],
      ),
      ProductModel(
        id: '2',
        title: 'سيارة تويوتا كامري 2022',
        description: 'سيارة نظيفة جداً، صيانة منتظمة، بحالة الوكالة',
        price: 85000,
        category: 'سيارات',
        condition: 'مستعمل - ممتاز',
        location: 'جدة',
        sellerId: currentUser.id,
        sellerName: currentUser.displayName,
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        isFeatured: false,
        images: [],
        tags: ['تويوتا', 'كامري', 'سيارة'],
      ),
      ProductModel(
        id: '3',
        title: 'شقة للإيجار 3 غرف وصالة',
        description: 'شقة مفروشة بالكامل، موقع مميز، قريبة من الخدمات',
        price: 2500,
        category: 'عقارات',
        condition: 'جديد',
        location: 'الدمام',
        sellerId: currentUser.id,
        sellerName: currentUser.displayName,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isFeatured: true,
        images: [],
        tags: ['شقة', 'إيجار', 'مفروشة'],
      ),
    ];

    for (final product in mockProducts) {
      try {
        await _databaseService.insertProduct(product);
      } catch (e) {
        debugPrint('Error adding mock product: $e');
      }
    }
  }

  // Clear cache
  void clearCache() {
    _products.clear();
    _myProducts.clear();
    notifyListeners();
  }
}

class ProductResult {
  final bool isSuccess;
  final String message;
  final ProductModel? product;

  ProductResult._(this.isSuccess, this.message, [this.product]);

  factory ProductResult.success(String message, [ProductModel? product]) {
    return ProductResult._(true, message, product);
  }

  factory ProductResult.error(String message) {
    return ProductResult._(false, message);
  }
}
