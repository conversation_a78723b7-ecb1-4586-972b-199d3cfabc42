[{"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/animator/fragment_open_enter.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-fragment-1.7.1-2:/animator/fragment_open_enter.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/animator/fragment_close_enter.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-fragment-1.7.1-2:/animator/fragment_close_enter.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/animator/fragment_open_exit.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-fragment-1.7.1-2:/animator/fragment_open_exit.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/animator/fragment_close_exit.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-fragment-1.7.1-2:/animator/fragment_close_exit.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/animator/fragment_fade_exit.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-fragment-1.7.1-2:/animator/fragment_fade_exit.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/animator/fragment_fade_enter.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-fragment-1.7.1-2:/animator/fragment_fade_enter.xml"}]