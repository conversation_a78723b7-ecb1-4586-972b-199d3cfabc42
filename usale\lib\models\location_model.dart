enum LocationType {
  current,
  home,
  work,
  saved,
  product,
  mall,
  university,
  hospital,
  restaurant,
  other,
}

class LocationModel {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String city;
  final String country;
  final String? state;
  final String? postalCode;
  final LocationType type;
  final String? productId;
  final String? userId;
  final double? distance;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  LocationModel({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.country,
    this.state,
    this.postalCode,
    this.type = LocationType.other,
    this.productId,
    this.userId,
    this.distance,
    this.metadata = const {},
    required this.createdAt,
    this.updatedAt,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      state: json['state'],
      postalCode: json['postal_code'],
      type: LocationType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'other'),
        orElse: () => LocationType.other,
      ),
      productId: json['product_id'],
      userId: json['user_id'],
      distance: json['distance']?.toDouble(),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'city': city,
      'country': country,
      'state': state,
      'postal_code': postalCode,
      'type': type.toString().split('.').last,
      'product_id': productId,
      'user_id': userId,
      'distance': distance,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  LocationModel copyWith({
    String? id,
    String? name,
    String? address,
    double? latitude,
    double? longitude,
    String? city,
    String? country,
    String? state,
    String? postalCode,
    LocationType? type,
    String? productId,
    String? userId,
    double? distance,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LocationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      country: country ?? this.country,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      type: type ?? this.type,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      distance: distance ?? this.distance,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get fullAddress {
    final parts = <String>[];
    
    if (address.isNotEmpty) parts.add(address);
    if (city.isNotEmpty) parts.add(city);
    if (state != null && state!.isNotEmpty) parts.add(state!);
    if (country.isNotEmpty) parts.add(country);
    
    return parts.join(', ');
  }

  String get shortAddress {
    final parts = <String>[];
    
    if (city.isNotEmpty) parts.add(city);
    if (country.isNotEmpty) parts.add(country);
    
    return parts.join(', ');
  }

  String get distanceText {
    if (distance == null) return '';
    
    if (distance! < 1) {
      return '${(distance! * 1000).round()} م';
    } else {
      return '${distance!.toStringAsFixed(1)} كم';
    }
  }

  String get typeText {
    switch (type) {
      case LocationType.current:
        return 'الموقع الحالي';
      case LocationType.home:
        return 'المنزل';
      case LocationType.work:
        return 'العمل';
      case LocationType.saved:
        return 'محفوظ';
      case LocationType.product:
        return 'منتج';
      case LocationType.mall:
        return 'مول';
      case LocationType.university:
        return 'جامعة';
      case LocationType.hospital:
        return 'مستشفى';
      case LocationType.restaurant:
        return 'مطعم';
      case LocationType.other:
        return 'أخرى';
    }
  }

  String get coordinates {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  // Generate Google Maps URL
  String get googleMapsUrl {
    return 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
  }

  // Generate Apple Maps URL
  String get appleMapsUrl {
    return 'http://maps.apple.com/?q=$latitude,$longitude';
  }

  // Check if location is valid
  bool get isValid {
    return latitude >= -90 && latitude <= 90 && 
           longitude >= -180 && longitude <= 180 &&
           name.isNotEmpty && address.isNotEmpty;
  }

  @override
  String toString() {
    return 'LocationModel(id: $id, name: $name, coordinates: $coordinates)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class LocationBounds {
  final double northEastLat;
  final double northEastLng;
  final double southWestLat;
  final double southWestLng;

  LocationBounds({
    required this.northEastLat,
    required this.northEastLng,
    required this.southWestLat,
    required this.southWestLng,
  });

  factory LocationBounds.fromJson(Map<String, dynamic> json) {
    return LocationBounds(
      northEastLat: json['northeast']['lat'].toDouble(),
      northEastLng: json['northeast']['lng'].toDouble(),
      southWestLat: json['southwest']['lat'].toDouble(),
      southWestLng: json['southwest']['lng'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'northeast': {
        'lat': northEastLat,
        'lng': northEastLng,
      },
      'southwest': {
        'lat': southWestLat,
        'lng': southWestLng,
      },
    };
  }

  LocationModel get center {
    final centerLat = (northEastLat + southWestLat) / 2;
    final centerLng = (northEastLng + southWestLng) / 2;
    
    return LocationModel(
      id: 'center',
      name: 'المركز',
      address: '',
      latitude: centerLat,
      longitude: centerLng,
      city: '',
      country: '',
      createdAt: DateTime.now(),
    );
  }

  bool contains(LocationModel location) {
    return location.latitude >= southWestLat &&
           location.latitude <= northEastLat &&
           location.longitude >= southWestLng &&
           location.longitude <= northEastLng;
  }
}
