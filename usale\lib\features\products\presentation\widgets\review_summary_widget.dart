import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../services/reviews_service.dart';

class ReviewSummaryWidget extends StatelessWidget {
  final String productId;
  final double averageRating;
  final int totalReviews;

  const ReviewSummaryWidget({
    super.key,
    required this.productId,
    required this.averageRating,
    required this.totalReviews,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ReviewsService>(
      builder: (context, reviewsService, child) {
        final summary = reviewsService.getReviewSummary(productId);
        
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Overall Rating
                Row(
                  children: [
                    // Rating Number
                    Column(
                      children: [
                        Text(
                          averageRating.toStringAsFixed(1),
                          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        Row(
                          children: List.generate(5, (index) {
                            return Icon(
                              index < averageRating.round()
                                  ? Icons.star
                                  : Icons.star_border,
                              color: AppColors.warning,
                              size: 20,
                            );
                          }),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$totalReviews تقييم',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(width: 24),

                    // Rating Distribution
                    Expanded(
                      child: Column(
                        children: List.generate(5, (index) {
                          final stars = 5 - index;
                          final count = summary?.ratingDistribution[stars] ?? 0;
                          final percentage = totalReviews > 0 
                              ? (count / totalReviews) * 100 
                              : 0.0;
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              children: [
                                Text(
                                  '$stars',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                const Icon(
                                  Icons.star,
                                  size: 12,
                                  color: AppColors.warning,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: LinearProgressIndicator(
                                    value: percentage / 100,
                                    backgroundColor: AppColors.border,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.primary,
                                    ),
                                    minHeight: 6,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                SizedBox(
                                  width: 30,
                                  child: Text(
                                    '$count',
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: AppColors.textSecondary,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Quick Stats
                if (summary != null) ...[
                  const Divider(),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildQuickStat(
                        context,
                        'مع صور',
                        '${summary.reviewsWithImages}',
                        Icons.photo_outlined,
                      ),
                      _buildQuickStat(
                        context,
                        'مع تعليقات',
                        '${summary.reviewsWithComments}',
                        Icons.comment_outlined,
                      ),
                      _buildQuickStat(
                        context,
                        'مفيدة',
                        '${summary.helpfulReviews}',
                        Icons.thumb_up_outlined,
                      ),
                      _buildQuickStat(
                        context,
                        'موثقة',
                        '${summary.verifiedPurchases}',
                        Icons.verified_outlined,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickStat(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.primary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
