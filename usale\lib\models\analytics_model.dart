enum EventCategory {
  navigation,
  product,
  search,
  ecommerce,
  engagement,
  social,
  user,
  system,
}

class AnalyticsEvent {
  final String id;
  final String? userId;
  final String eventName;
  final EventCategory category;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;

  AnalyticsEvent({
    required this.id,
    this.userId,
    required this.eventName,
    required this.category,
    this.parameters = const {},
    required this.timestamp,
  });

  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) {
    return AnalyticsEvent(
      id: json['id'] ?? '',
      userId: json['user_id'],
      eventName: json['event_name'] ?? '',
      category: EventCategory.values.firstWhere(
        (e) => e.toString().split('.').last == (json['category'] ?? 'system'),
        orElse: () => EventCategory.system,
      ),
      parameters: Map<String, dynamic>.from(json['parameters'] ?? {}),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'event_name': eventName,
      'category': category.toString().split('.').last,
      'parameters': parameters,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'AnalyticsEvent(eventName: $eventName, category: $category, userId: $userId)';
  }
}

class UserAnalytics {
  final String userId;
  final int totalViews;
  final int totalProducts;
  final int totalSales;
  final double totalRevenue;
  final double averageRating;
  final int totalReviews;
  final int profileViews;
  final int favoriteCount;
  final int messagesSent;
  final int messagesReceived;
  final DateTime? lastActiveAt;

  UserAnalytics({
    required this.userId,
    required this.totalViews,
    required this.totalProducts,
    required this.totalSales,
    required this.totalRevenue,
    required this.averageRating,
    required this.totalReviews,
    required this.profileViews,
    required this.favoriteCount,
    required this.messagesSent,
    required this.messagesReceived,
    this.lastActiveAt,
  });

  factory UserAnalytics.fromJson(Map<String, dynamic> json) {
    return UserAnalytics(
      userId: json['user_id'] ?? '',
      totalViews: json['total_views'] ?? 0,
      totalProducts: json['total_products'] ?? 0,
      totalSales: json['total_sales'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      profileViews: json['profile_views'] ?? 0,
      favoriteCount: json['favorite_count'] ?? 0,
      messagesSent: json['messages_sent'] ?? 0,
      messagesReceived: json['messages_received'] ?? 0,
      lastActiveAt: json['last_active_at'] != null 
          ? DateTime.parse(json['last_active_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'total_views': totalViews,
      'total_products': totalProducts,
      'total_sales': totalSales,
      'total_revenue': totalRevenue,
      'average_rating': averageRating,
      'total_reviews': totalReviews,
      'profile_views': profileViews,
      'favorite_count': favoriteCount,
      'messages_sent': messagesSent,
      'messages_received': messagesReceived,
      'last_active_at': lastActiveAt?.toIso8601String(),
    };
  }

  double get conversionRate {
    if (totalViews == 0) return 0.0;
    return totalSales / totalViews;
  }

  double get averageOrderValue {
    if (totalSales == 0) return 0.0;
    return totalRevenue / totalSales;
  }

  String get activityStatus {
    if (lastActiveAt == null) return 'غير معروف';
    
    final now = DateTime.now();
    final difference = now.difference(lastActiveAt!);
    
    if (difference.inMinutes < 5) return 'نشط الآن';
    if (difference.inMinutes < 60) return 'نشط منذ ${difference.inMinutes} دقيقة';
    if (difference.inHours < 24) return 'نشط منذ ${difference.inHours} ساعة';
    if (difference.inDays < 7) return 'نشط منذ ${difference.inDays} يوم';
    return 'غير نشط';
  }
}

class ProductAnalytics {
  final int totalProducts;
  final int activeProducts;
  final int soldProducts;
  final int totalViews;
  final int totalFavorites;
  final double averageViews;
  final String topCategory;
  final String bestPerformingProduct;
  final double conversionRate;

  ProductAnalytics({
    required this.totalProducts,
    required this.activeProducts,
    required this.soldProducts,
    required this.totalViews,
    required this.totalFavorites,
    required this.averageViews,
    required this.topCategory,
    required this.bestPerformingProduct,
    required this.conversionRate,
  });

  factory ProductAnalytics.fromJson(Map<String, dynamic> json) {
    return ProductAnalytics(
      totalProducts: json['total_products'] ?? 0,
      activeProducts: json['active_products'] ?? 0,
      soldProducts: json['sold_products'] ?? 0,
      totalViews: json['total_views'] ?? 0,
      totalFavorites: json['total_favorites'] ?? 0,
      averageViews: (json['average_views'] ?? 0.0).toDouble(),
      topCategory: json['top_category'] ?? '',
      bestPerformingProduct: json['best_performing_product'] ?? '',
      conversionRate: (json['conversion_rate'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_products': totalProducts,
      'active_products': activeProducts,
      'sold_products': soldProducts,
      'total_views': totalViews,
      'total_favorites': totalFavorites,
      'average_views': averageViews,
      'top_category': topCategory,
      'best_performing_product': bestPerformingProduct,
      'conversion_rate': conversionRate,
    };
  }

  double get soldPercentage {
    if (totalProducts == 0) return 0.0;
    return soldProducts / totalProducts;
  }

  double get favoriteRate {
    if (totalViews == 0) return 0.0;
    return totalFavorites / totalViews;
  }
}

class SalesAnalytics {
  final int totalSales;
  final double totalRevenue;
  final double averageOrderValue;
  final int salesThisMonth;
  final double revenueThisMonth;
  final int salesLastMonth;
  final double revenueLastMonth;
  final String topSellingCategory;
  final double salesGrowth;
  final double revenueGrowth;

  SalesAnalytics({
    required this.totalSales,
    required this.totalRevenue,
    required this.averageOrderValue,
    required this.salesThisMonth,
    required this.revenueThisMonth,
    required this.salesLastMonth,
    required this.revenueLastMonth,
    required this.topSellingCategory,
    required this.salesGrowth,
    required this.revenueGrowth,
  });

  factory SalesAnalytics.fromJson(Map<String, dynamic> json) {
    return SalesAnalytics(
      totalSales: json['total_sales'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      averageOrderValue: (json['average_order_value'] ?? 0.0).toDouble(),
      salesThisMonth: json['sales_this_month'] ?? 0,
      revenueThisMonth: (json['revenue_this_month'] ?? 0.0).toDouble(),
      salesLastMonth: json['sales_last_month'] ?? 0,
      revenueLastMonth: (json['revenue_last_month'] ?? 0.0).toDouble(),
      topSellingCategory: json['top_selling_category'] ?? '',
      salesGrowth: (json['sales_growth'] ?? 0.0).toDouble(),
      revenueGrowth: (json['revenue_growth'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_sales': totalSales,
      'total_revenue': totalRevenue,
      'average_order_value': averageOrderValue,
      'sales_this_month': salesThisMonth,
      'revenue_this_month': revenueThisMonth,
      'sales_last_month': salesLastMonth,
      'revenue_last_month': revenueLastMonth,
      'top_selling_category': topSellingCategory,
      'sales_growth': salesGrowth,
      'revenue_growth': revenueGrowth,
    };
  }

  String get salesGrowthText {
    if (salesGrowth > 0) {
      return '+${(salesGrowth * 100).toStringAsFixed(1)}%';
    } else {
      return '${(salesGrowth * 100).toStringAsFixed(1)}%';
    }
  }

  String get revenueGrowthText {
    if (revenueGrowth > 0) {
      return '+${(revenueGrowth * 100).toStringAsFixed(1)}%';
    } else {
      return '${(revenueGrowth * 100).toStringAsFixed(1)}%';
    }
  }
}

class AnalyticsData {
  final int totalEvents;
  final int uniqueUsers;
  final Map<EventCategory, int> eventsByCategory;
  final DateTime startDate;
  final DateTime endDate;

  AnalyticsData({
    required this.totalEvents,
    required this.uniqueUsers,
    required this.eventsByCategory,
    required this.startDate,
    required this.endDate,
  });

  Duration get dateRange => endDate.difference(startDate);

  double get averageEventsPerDay {
    final days = dateRange.inDays;
    if (days == 0) return totalEvents.toDouble();
    return totalEvents / days;
  }
}

class ProductPerformance {
  final String productId;
  final String productTitle;
  final int views;
  final int favorites;
  final int purchases;

  ProductPerformance({
    required this.productId,
    required this.productTitle,
    required this.views,
    required this.favorites,
    required this.purchases,
  });

  double get conversionRate {
    if (views == 0) return 0.0;
    return purchases / views;
  }

  double get favoriteRate {
    if (views == 0) return 0.0;
    return favorites / views;
  }
}

class UserEngagement {
  final int eventsLast7Days;
  final int eventsLast30Days;
  final Duration averageSessionLength;
  final double bounceRate;

  UserEngagement({
    required this.eventsLast7Days,
    required this.eventsLast30Days,
    required this.averageSessionLength,
    required this.bounceRate,
  });

  double get engagementTrend {
    if (eventsLast30Days == 0) return 0.0;
    final weeklyAverage = eventsLast30Days / 4.0;
    if (weeklyAverage == 0) return 0.0;
    return (eventsLast7Days - weeklyAverage) / weeklyAverage;
  }

  String get engagementLevel {
    if (eventsLast7Days > 50) return 'عالي';
    if (eventsLast7Days > 20) return 'متوسط';
    if (eventsLast7Days > 5) return 'منخفض';
    return 'ضعيف جداً';
  }
}
