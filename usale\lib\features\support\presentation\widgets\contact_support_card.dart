import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ContactSupportCard extends StatelessWidget {
  const ContactSupportCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.support_agent,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'تواصل مع فريق الدعم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'فريقنا متاح لمساعدتك 24/7',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 20),
            
            // Contact Options
            Row(
              children: [
                Expanded(
                  child: _buildContactOption(
                    icon: Icons.chat,
                    title: 'دردشة مباشرة',
                    subtitle: 'رد فوري',
                    color: AppColors.primary,
                    onTap: () => _startLiveChat(context),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildContactOption(
                    icon: Icons.email,
                    title: 'البريد الإلكتروني',
                    subtitle: 'خلال 24 ساعة',
                    color: AppColors.success,
                    onTap: () => _sendEmail(),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildContactOption(
                    icon: Icons.phone,
                    title: 'اتصال هاتفي',
                    subtitle: '920001234',
                    color: AppColors.warning,
                    onTap: () => _makePhoneCall(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildContactOption(
                    icon: Icons.message,
                    title: 'واتساب',
                    subtitle: 'رسالة سريعة',
                    color: AppColors.success,
                    onTap: () => _sendWhatsApp(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _startLiveChat(BuildContext context) {
    // Navigate to live chat page
    Navigator.pushNamed(context, '/live-chat');
  }

  void _sendEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=طلب مساعدة من تطبيق يوسيل',
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _makePhoneCall() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '+966920001234');
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  void _sendWhatsApp() async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/966920001234?text=مرحباً، أحتاج مساعدة في تطبيق يوسيل',
    );
    
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }
}
