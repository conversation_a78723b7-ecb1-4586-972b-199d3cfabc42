{"roots": ["usale"], "packages": [{"name": "usale", "version": "1.0.0+1", "dependencies": ["animate_do", "cached_network_image", "connectivity_plus", "country_code_picker", "crypto", "cupertino_icons", "device_info_plus", "dio", "flutter", "flutter_local_notifications", "flutter_localizations", "flutter_staggered_animations", "flutter_svg", "geocoding", "geolocator", "get", "go_router", "google_fonts", "google_maps_flutter", "google_sign_in", "hive", "hive_flutter", "http", "image_picker", "intl", "json_annotation", "lottie", "package_info_plus", "path", "permission_handler", "photo_view", "pin_code_fields", "provider", "share_plus", "shared_preferences", "shimmer", "socket_io_client", "sqflite", "url_launcher", "video_player"], "devDependencies": ["build_runner", "flutter_launcher_icons", "flutter_lints", "flutter_test", "json_serializable"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "json_serializable", "version": "6.9.5", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "build_runner", "version": "2.5.3", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "animate_do", "version": "3.3.9", "dependencies": ["flutter"]}, {"name": "flutter_staggered_animations", "version": "1.1.1", "dependencies": ["flutter"]}, {"name": "socket_io_client", "version": "2.0.3+1", "dependencies": ["js", "logging", "socket_io_common"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "device_info_plus", "version": "10.1.2", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "share_plus", "version": "10.1.4", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "google_maps_flutter", "version": "2.12.3", "dependencies": ["flutter", "google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_platform_interface", "google_maps_flutter_web"]}, {"name": "geocoding", "version": "3.0.0", "dependencies": ["flutter", "geocoding_android", "geocoding_ios", "geocoding_platform_interface"]}, {"name": "geolocator", "version": "12.0.0", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "video_player", "version": "2.10.0", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "photo_view", "version": "0.15.0", "dependencies": ["flutter"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "pin_code_fields", "version": "8.0.1", "dependencies": ["flutter"]}, {"name": "country_code_picker", "version": "3.3.0", "dependencies": ["collection", "diacritic", "flutter"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "google_sign_in", "version": "6.3.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "flutter_local_notifications", "version": "17.2.4", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "connectivity_plus", "version": "6.1.4", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "go_router", "version": "14.8.1", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "get", "version": "4.7.2", "dependencies": ["flutter", "web"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "lottie", "version": "3.3.1", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "flutter_svg", "version": "2.2.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.5.3", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "analyzer", "version": "7.4.5", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "9.1.1", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.3", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "socket_io_common", "version": "2.0.3", "dependencies": ["logging"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "win32_registry", "version": "1.1.5", "dependencies": ["ffi", "win32"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "share_plus_platform_interface", "version": "5.0.2", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "google_maps_flutter_web", "version": "0.5.12+1", "dependencies": ["collection", "flutter", "flutter_web_plugins", "google_maps", "google_maps_flutter_platform_interface", "sanitize_html", "stream_transform", "web"]}, {"name": "google_maps_flutter_platform_interface", "version": "2.12.1", "dependencies": ["collection", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "google_maps_flutter_ios", "version": "2.15.4", "dependencies": ["flutter", "google_maps_flutter_platform_interface", "stream_transform"]}, {"name": "google_maps_flutter_android", "version": "2.16.1", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "google_maps_flutter_platform_interface", "stream_transform"]}, {"name": "geocoding_ios", "version": "3.0.2", "dependencies": ["flutter", "geocoding_platform_interface"]}, {"name": "geocoding_android", "version": "3.3.1", "dependencies": ["flutter", "geocoding_platform_interface"]}, {"name": "geocoding_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "4.1.3", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface", "web"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "4.6.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "video_player_web", "version": "2.3.5", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.3.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.7.1", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.7", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "diacritic", "version": "0.1.6", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "google_sign_in_web", "version": "0.12.4+4", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "2.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "5.9.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "6.2.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "_fe_analyzer_shared", "version": "82.0.0", "dependencies": ["meta"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "sanitize_html", "version": "2.1.0", "dependencies": ["html", "meta"]}, {"name": "google_maps", "version": "8.1.1", "dependencies": ["meta", "web"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "google_identity_services_web", "version": "0.3.3+1", "dependencies": ["meta", "web"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}], "configVersion": 1}