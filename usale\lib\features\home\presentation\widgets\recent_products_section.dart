import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';

class RecentProductsSection extends StatelessWidget {
  const RecentProductsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              const Icon(
                Icons.access_time,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'أحدث المنتجات',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // Navigate to all recent products
                },
                child: const Text('عرض الكل'),
              ),
            ],
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
          ),
          itemCount: _mockRecentProducts.length,
          itemBuilder: (context, index) {
            final product = _mockRecentProducts[index];
            return _buildRecentProductCard(context, product);
          },
        ),
      ],
    );
  }

  Widget _buildRecentProductCard(BuildContext context, ProductModel product) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Product Image
            Stack(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.image,
                    size: 32,
                    color: AppColors.textLight,
                  ),
                ),
                if (product.isFeatured)
                  Positioned(
                    top: 4,
                    left: 4,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: const BoxDecoration(
                        color: AppColors.accent,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.star,
                        size: 10,
                        color: AppColors.white,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 12),
            
            // Product Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    product.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  
                  // Price
                  Text(
                    product.formattedPrice,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  
                  // Location and Time
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on_outlined,
                        size: 14,
                        color: AppColors.textLight,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        product.location,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.access_time,
                        size: 14,
                        color: AppColors.textLight,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        product.timeAgo,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textLight,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  
                  // Category and Condition
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          product.category,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          product.condition,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: AppColors.success,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Actions
            Column(
              children: [
                IconButton(
                  onPressed: () {
                    // Toggle favorite
                  },
                  icon: const Icon(
                    Icons.favorite_outline,
                    color: AppColors.textLight,
                    size: 20,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // Contact seller
                  },
                  icon: const Icon(
                    Icons.chat_bubble_outline,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static final List<ProductModel> _mockRecentProducts = [
    ProductModel(
      id: '4',
      title: 'لابتوب ديل XPS 13',
      description: 'لابتوب خفيف ومحمول، مناسب للعمل والدراسة',
      price: 3200,
      category: 'إلكترونيات',
      condition: 'مستعمل - ممتاز',
      location: 'الرياض',
      sellerId: 'seller4',
      sellerName: 'سارة أحمد',
      createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
    ),
    ProductModel(
      id: '5',
      title: 'ساعة أبل الجيل الثامن',
      description: 'ساعة ذكية بحالة ممتازة مع جميع الإكسسوارات',
      price: 1200,
      category: 'إلكترونيات',
      condition: 'مستعمل - جيد',
      location: 'جدة',
      sellerId: 'seller5',
      sellerName: 'خالد محمد',
      createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      isFeatured: true,
    ),
    ProductModel(
      id: '6',
      title: 'دراجة هوائية جبلية',
      description: 'دراجة هوائية للبيع، مناسبة للرياضة والتنقل',
      price: 800,
      category: 'رياضة',
      condition: 'مستعمل - جيد',
      location: 'الدمام',
      sellerId: 'seller6',
      sellerName: 'عبدالله علي',
      createdAt: DateTime.now().subtract(const Duration(hours: 3)),
    ),
    ProductModel(
      id: '7',
      title: 'كاميرا كانون EOS R5',
      description: 'كاميرا احترافية للتصوير الفوتوغرافي والفيديو',
      price: 8500,
      category: 'إلكترونيات',
      condition: 'جديد',
      location: 'الرياض',
      sellerId: 'seller7',
      sellerName: 'نورا سعد',
      createdAt: DateTime.now().subtract(const Duration(hours: 6)),
    ),
  ];
}
