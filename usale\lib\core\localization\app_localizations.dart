import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('ar', 'SA'), // Arabic
    Locale('en', 'US'), // English
  ];

  // App Name and General
  String get appName => _localizedValues[locale.languageCode]!['app_name']!;
  String get welcome => _localizedValues[locale.languageCode]!['welcome']!;
  String get loading => _localizedValues[locale.languageCode]!['loading']!;
  String get error => _localizedValues[locale.languageCode]!['error']!;
  String get success => _localizedValues[locale.languageCode]!['success']!;
  String get cancel => _localizedValues[locale.languageCode]!['cancel']!;
  String get confirm => _localizedValues[locale.languageCode]!['confirm']!;
  String get save => _localizedValues[locale.languageCode]!['save']!;
  String get delete => _localizedValues[locale.languageCode]!['delete']!;
  String get edit => _localizedValues[locale.languageCode]!['edit']!;
  String get add => _localizedValues[locale.languageCode]!['add']!;
  String get search => _localizedValues[locale.languageCode]!['search']!;
  String get filter => _localizedValues[locale.languageCode]!['filter']!;
  String get sort => _localizedValues[locale.languageCode]!['sort']!;
  String get viewAll => _localizedValues[locale.languageCode]!['view_all']!;

  // Navigation
  String get home => _localizedValues[locale.languageCode]!['home']!;
  String get favorites => _localizedValues[locale.languageCode]!['favorites']!;
  String get messages => _localizedValues[locale.languageCode]!['messages']!;
  String get profile => _localizedValues[locale.languageCode]!['profile']!;
  String get settings => _localizedValues[locale.languageCode]!['settings']!;
  String get notifications => _localizedValues[locale.languageCode]!['notifications']!;

  // Home Page
  String get welcomeToMarketplace => _localizedValues[locale.languageCode]!['welcome_to_marketplace']!;
  String get categories => _localizedValues[locale.languageCode]!['categories']!;
  String get featuredProducts => _localizedValues[locale.languageCode]!['featured_products']!;
  String get recentProducts => _localizedValues[locale.languageCode]!['recent_products']!;
  String get searchPlaceholder => _localizedValues[locale.languageCode]!['search_placeholder']!;

  // Categories
  String get cars => _localizedValues[locale.languageCode]!['cars']!;
  String get realEstate => _localizedValues[locale.languageCode]!['real_estate']!;
  String get electronics => _localizedValues[locale.languageCode]!['electronics']!;
  String get fashion => _localizedValues[locale.languageCode]!['fashion']!;
  String get jobs => _localizedValues[locale.languageCode]!['jobs']!;
  String get services => _localizedValues[locale.languageCode]!['services']!;
  String get sports => _localizedValues[locale.languageCode]!['sports']!;
  String get books => _localizedValues[locale.languageCode]!['books']!;

  // Product
  String get product => _localizedValues[locale.languageCode]!['product']!;
  String get products => _localizedValues[locale.languageCode]!['products']!;
  String get price => _localizedValues[locale.languageCode]!['price']!;
  String get condition => _localizedValues[locale.languageCode]!['condition']!;
  String get location => _localizedValues[locale.languageCode]!['location']!;
  String get description => _localizedValues[locale.languageCode]!['description']!;
  String get contactSeller => _localizedValues[locale.languageCode]!['contact_seller']!;
  String get addToFavorites => _localizedValues[locale.languageCode]!['add_to_favorites']!;
  String get removeFromFavorites => _localizedValues[locale.languageCode]!['remove_from_favorites']!;
  String get share => _localizedValues[locale.languageCode]!['share']!;

  // Add Product
  String get addProduct => _localizedValues[locale.languageCode]!['add_product']!;
  String get addNewProduct => _localizedValues[locale.languageCode]!['add_new_product']!;
  String get productTitle => _localizedValues[locale.languageCode]!['product_title']!;
  String get productDescription => _localizedValues[locale.languageCode]!['product_description']!;
  String get productImages => _localizedValues[locale.languageCode]!['product_images']!;
  String get addImage => _localizedValues[locale.languageCode]!['add_image']!;
  String get publish => _localizedValues[locale.languageCode]!['publish']!;
  String get publishAd => _localizedValues[locale.languageCode]!['publish_ad']!;

  // Profile
  String get myProfile => _localizedValues[locale.languageCode]!['my_profile']!;
  String get editProfile => _localizedValues[locale.languageCode]!['edit_profile']!;
  String get myProducts => _localizedValues[locale.languageCode]!['my_products']!;
  String get myPurchases => _localizedValues[locale.languageCode]!['my_purchases']!;
  String get accountSettings => _localizedValues[locale.languageCode]!['account_settings']!;
  String get logout => _localizedValues[locale.languageCode]!['logout']!;
  String get login => _localizedValues[locale.languageCode]!['login']!;
  String get register => _localizedValues[locale.languageCode]!['register']!;
  String get createNewAccount => _localizedValues[locale.languageCode]!['create_new_account']!;

  // Settings
  String get language => _localizedValues[locale.languageCode]!['language']!;
  String get arabic => _localizedValues[locale.languageCode]!['arabic']!;
  String get english => _localizedValues[locale.languageCode]!['english']!;
  String get darkMode => _localizedValues[locale.languageCode]!['dark_mode']!;
  String get enableNotifications => _localizedValues[locale.languageCode]!['enable_notifications']!;
  String get privacyAndSecurity => _localizedValues[locale.languageCode]!['privacy_and_security']!;

  // Messages
  String get chat => _localizedValues[locale.languageCode]!['chat']!;
  String get typeMessage => _localizedValues[locale.languageCode]!['type_message']!;
  String get online => _localizedValues[locale.languageCode]!['online']!;
  String get offline => _localizedValues[locale.languageCode]!['offline']!;
  String get lastSeen => _localizedValues[locale.languageCode]!['last_seen']!;

  // Time
  String get now => _localizedValues[locale.languageCode]!['now']!;
  String get minutesAgo => _localizedValues[locale.languageCode]!['minutes_ago']!;
  String get hoursAgo => _localizedValues[locale.languageCode]!['hours_ago']!;
  String get daysAgo => _localizedValues[locale.languageCode]!['days_ago']!;

  // Status
  String get active => _localizedValues[locale.languageCode]!['active']!;
  String get sold => _localizedValues[locale.languageCode]!['sold']!;
  String get expired => _localizedValues[locale.languageCode]!['expired']!;
  String get available => _localizedValues[locale.languageCode]!['available']!;
  String get reserved => _localizedValues[locale.languageCode]!['reserved']!;

  // Empty States
  String get noFavorites => _localizedValues[locale.languageCode]!['no_favorites']!;
  String get noMessages => _localizedValues[locale.languageCode]!['no_messages']!;
  String get noNotifications => _localizedValues[locale.languageCode]!['no_notifications']!;
  String get noProducts => _localizedValues[locale.languageCode]!['no_products']!;
  String get noResults => _localizedValues[locale.languageCode]!['no_results']!;

  static const Map<String, Map<String, String>> _localizedValues = {
    'ar': {
      'app_name': 'يو سيل',
      'welcome': 'مرحباً',
      'loading': 'جاري التحميل...',
      'error': 'خطأ',
      'success': 'نجح',
      'cancel': 'إلغاء',
      'confirm': 'تأكيد',
      'save': 'حفظ',
      'delete': 'حذف',
      'edit': 'تعديل',
      'add': 'إضافة',
      'search': 'البحث',
      'filter': 'فلترة',
      'sort': 'ترتيب',
      'view_all': 'عرض الكل',
      
      // Navigation
      'home': 'الرئيسية',
      'favorites': 'المفضلة',
      'messages': 'الرسائل',
      'profile': 'الملف الشخصي',
      'settings': 'الإعدادات',
      'notifications': 'الإشعارات',
      
      // Home Page
      'welcome_to_marketplace': 'مرحباً بك في السوق الإلكتروني',
      'categories': 'الفئات',
      'featured_products': 'المنتجات المميزة',
      'recent_products': 'أحدث المنتجات',
      'search_placeholder': 'ابحث عن منتجات...',
      
      // Categories
      'cars': 'سيارات',
      'real_estate': 'عقارات',
      'electronics': 'إلكترونيات',
      'fashion': 'أزياء',
      'jobs': 'وظائف',
      'services': 'خدمات',
      'sports': 'رياضة',
      'books': 'كتب',
      
      // Product
      'product': 'منتج',
      'products': 'منتجات',
      'price': 'السعر',
      'condition': 'الحالة',
      'location': 'الموقع',
      'description': 'الوصف',
      'contact_seller': 'تواصل مع البائع',
      'add_to_favorites': 'إضافة للمفضلة',
      'remove_from_favorites': 'إزالة من المفضلة',
      'share': 'مشاركة',
      
      // Add Product
      'add_product': 'إضافة منتج',
      'add_new_product': 'إضافة منتج جديد',
      'product_title': 'عنوان المنتج',
      'product_description': 'وصف المنتج',
      'product_images': 'صور المنتج',
      'add_image': 'إضافة صورة',
      'publish': 'نشر',
      'publish_ad': 'نشر الإعلان',
      
      // Profile
      'my_profile': 'ملفي الشخصي',
      'edit_profile': 'تعديل الملف الشخصي',
      'my_products': 'منتجاتي',
      'my_purchases': 'مشترياتي',
      'account_settings': 'إعدادات الحساب',
      'logout': 'تسجيل الخروج',
      'login': 'تسجيل الدخول',
      'register': 'تسجيل',
      'create_new_account': 'إنشاء حساب جديد',
      
      // Settings
      'language': 'اللغة',
      'arabic': 'العربية',
      'english': 'الإنجليزية',
      'dark_mode': 'الوضع الليلي',
      'enable_notifications': 'تفعيل الإشعارات',
      'privacy_and_security': 'الخصوصية والأمان',
      
      // Messages
      'chat': 'دردشة',
      'type_message': 'اكتب رسالة...',
      'online': 'متصل',
      'offline': 'غير متصل',
      'last_seen': 'آخر ظهور',
      
      // Time
      'now': 'الآن',
      'minutes_ago': 'منذ دقائق',
      'hours_ago': 'منذ ساعات',
      'days_ago': 'منذ أيام',
      
      // Status
      'active': 'نشط',
      'sold': 'مباع',
      'expired': 'منتهي',
      'available': 'متاح',
      'reserved': 'محجوز',
      
      // Empty States
      'no_favorites': 'لا توجد عناصر مفضلة',
      'no_messages': 'لا توجد رسائل',
      'no_notifications': 'لا توجد إشعارات',
      'no_products': 'لا توجد منتجات',
      'no_results': 'لا توجد نتائج',
    },
    'en': {
      'app_name': 'USale',
      'welcome': 'Welcome',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'filter': 'Filter',
      'sort': 'Sort',
      'view_all': 'View All',
      
      // Navigation
      'home': 'Home',
      'favorites': 'Favorites',
      'messages': 'Messages',
      'profile': 'Profile',
      'settings': 'Settings',
      'notifications': 'Notifications',
      
      // Home Page
      'welcome_to_marketplace': 'Welcome to the Marketplace',
      'categories': 'Categories',
      'featured_products': 'Featured Products',
      'recent_products': 'Recent Products',
      'search_placeholder': 'Search for products...',
      
      // Categories
      'cars': 'Cars',
      'real_estate': 'Real Estate',
      'electronics': 'Electronics',
      'fashion': 'Fashion',
      'jobs': 'Jobs',
      'services': 'Services',
      'sports': 'Sports',
      'books': 'Books',
      
      // Product
      'product': 'Product',
      'products': 'Products',
      'price': 'Price',
      'condition': 'Condition',
      'location': 'Location',
      'description': 'Description',
      'contact_seller': 'Contact Seller',
      'add_to_favorites': 'Add to Favorites',
      'remove_from_favorites': 'Remove from Favorites',
      'share': 'Share',
      
      // Add Product
      'add_product': 'Add Product',
      'add_new_product': 'Add New Product',
      'product_title': 'Product Title',
      'product_description': 'Product Description',
      'product_images': 'Product Images',
      'add_image': 'Add Image',
      'publish': 'Publish',
      'publish_ad': 'Publish Ad',
      
      // Profile
      'my_profile': 'My Profile',
      'edit_profile': 'Edit Profile',
      'my_products': 'My Products',
      'my_purchases': 'My Purchases',
      'account_settings': 'Account Settings',
      'logout': 'Logout',
      'login': 'Login',
      'register': 'Register',
      'create_new_account': 'Create New Account',
      
      // Settings
      'language': 'Language',
      'arabic': 'Arabic',
      'english': 'English',
      'dark_mode': 'Dark Mode',
      'enable_notifications': 'Enable Notifications',
      'privacy_and_security': 'Privacy & Security',
      
      // Messages
      'chat': 'Chat',
      'type_message': 'Type a message...',
      'online': 'Online',
      'offline': 'Offline',
      'last_seen': 'Last seen',
      
      // Time
      'now': 'Now',
      'minutes_ago': 'Minutes ago',
      'hours_ago': 'Hours ago',
      'days_ago': 'Days ago',
      
      // Status
      'active': 'Active',
      'sold': 'Sold',
      'expired': 'Expired',
      'available': 'Available',
      'reserved': 'Reserved',
      
      // Empty States
      'no_favorites': 'No favorites',
      'no_messages': 'No messages',
      'no_notifications': 'No notifications',
      'no_products': 'No products',
      'no_results': 'No results',
    },
  };
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
