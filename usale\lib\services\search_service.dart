import 'package:flutter/foundation.dart';
import '../models/product_model.dart';
import '../services/database_service.dart';
import '../core/constants/app_constants.dart';

class SearchService extends ChangeNotifier {
  // Singleton pattern
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  final DatabaseService _databaseService = DatabaseService();

  List<ProductModel> _searchResults = [];
  List<String> _searchHistory = [];
  bool _isSearching = false;
  String _currentQuery = '';
  SearchFilters _currentFilters = SearchFilters();

  // Getters
  List<ProductModel> get searchResults => _searchResults;
  List<String> get searchHistory => _searchHistory;
  bool get isSearching => _isSearching;
  String get currentQuery => _currentQuery;
  SearchFilters get currentFilters => _currentFilters;

  // Search products with filters
  Future<void> searchProducts({
    required String query,
    SearchFilters? filters,
    SortOption? sortOption,
  }) async {
    _isSearching = true;
    _currentQuery = query;
    _currentFilters = filters ?? SearchFilters();
    notifyListeners();

    try {
      // Add to search history
      if (query.isNotEmpty && !_searchHistory.contains(query)) {
        _searchHistory.insert(0, query);
        if (_searchHistory.length > 10) {
          _searchHistory.removeLast();
        }
        await _saveSearchHistory();
      }

      // Get all products first
      List<ProductModel> allProducts = await _databaseService.getProducts();

      // Apply text search
      if (query.isNotEmpty) {
        allProducts = allProducts.where((product) {
          return product.title.toLowerCase().contains(query.toLowerCase()) ||
                 product.description.toLowerCase().contains(query.toLowerCase()) ||
                 product.category.toLowerCase().contains(query.toLowerCase()) ||
                 product.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
        }).toList();
      }

      // Apply filters
      allProducts = _applyFilters(allProducts, _currentFilters);

      // Apply sorting
      allProducts = _applySorting(allProducts, sortOption ?? SortOption.newest);

      _searchResults = allProducts;
    } catch (e) {
      debugPrint('Error searching products: $e');
      _searchResults = [];
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  // Apply filters to products
  List<ProductModel> _applyFilters(List<ProductModel> products, SearchFilters filters) {
    return products.where((product) {
      // Category filter
      if (filters.category != null && filters.category!.isNotEmpty) {
        if (product.category != filters.category) return false;
      }

      // Price range filter
      if (filters.minPrice != null && product.price < filters.minPrice!) return false;
      if (filters.maxPrice != null && product.price > filters.maxPrice!) return false;

      // Condition filter
      if (filters.condition != null && filters.condition!.isNotEmpty) {
        if (product.condition != filters.condition) return false;
      }

      // Location filter
      if (filters.location != null && filters.location!.isNotEmpty) {
        if (!product.location.toLowerCase().contains(filters.location!.toLowerCase())) {
          return false;
        }
      }

      // Date range filter
      if (filters.dateFrom != null && product.createdAt.isBefore(filters.dateFrom!)) {
        return false;
      }
      if (filters.dateTo != null && product.createdAt.isAfter(filters.dateTo!)) {
        return false;
      }

      // Has images filter
      if (filters.hasImages == true && product.images.isEmpty) return false;

      // Featured only filter
      if (filters.featuredOnly == true && !product.isFeatured) return false;

      // Available only filter
      if (filters.availableOnly == true && product.status != ProductStatus.available) {
        return false;
      }

      return true;
    }).toList();
  }

  // Apply sorting to products
  List<ProductModel> _applySorting(List<ProductModel> products, SortOption sortOption) {
    switch (sortOption) {
      case SortOption.newest:
        products.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.oldest:
        products.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case SortOption.priceLowToHigh:
        products.sort((a, b) => a.price.compareTo(b.price));
        break;
      case SortOption.priceHighToLow:
        products.sort((a, b) => b.price.compareTo(a.price));
        break;
      case SortOption.mostViewed:
        products.sort((a, b) => b.viewsCount.compareTo(a.viewsCount));
        break;
      case SortOption.mostFavorited:
        products.sort((a, b) => b.favoritesCount.compareTo(a.favoritesCount));
        break;
      case SortOption.alphabetical:
        products.sort((a, b) => a.title.compareTo(b.title));
        break;
    }
    return products;
  }

  // Get suggestions based on query
  List<String> getSuggestions(String query) {
    if (query.isEmpty) return _searchHistory;

    final suggestions = <String>[];
    
    // Add matching search history
    for (final historyItem in _searchHistory) {
      if (historyItem.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(historyItem);
      }
    }

    // Add matching categories
    for (final category in AppConstants.categories) {
      if (category.toLowerCase().contains(query.toLowerCase()) && 
          !suggestions.contains(category)) {
        suggestions.add(category);
      }
    }

    return suggestions.take(5).toList();
  }

  // Get popular searches
  List<String> getPopularSearches() {
    return [
      'آيفون',
      'سيارة',
      'شقة',
      'لابتوب',
      'ساعة',
      'جوال',
      'كاميرا',
      'أثاث',
    ];
  }

  // Clear search results
  void clearSearch() {
    _searchResults.clear();
    _currentQuery = '';
    _currentFilters = SearchFilters();
    notifyListeners();
  }

  // Clear search history
  Future<void> clearSearchHistory() async {
    _searchHistory.clear();
    await _saveSearchHistory();
    notifyListeners();
  }

  // Remove item from search history
  Future<void> removeFromHistory(String query) async {
    _searchHistory.remove(query);
    await _saveSearchHistory();
    notifyListeners();
  }

  // Load search history
  Future<void> loadSearchHistory() async {
    // In real app, load from SharedPreferences or database
    // For now, use mock data
    _searchHistory = [
      'آيفون 15',
      'سيارة تويوتا',
      'شقة للإيجار',
      'لابتوب ديل',
    ];
    notifyListeners();
  }

  // Save search history
  Future<void> _saveSearchHistory() async {
    // In real app, save to SharedPreferences or database
    // For now, just keep in memory
  }

  // Get filtered products count
  int getFilteredCount() {
    return _searchResults.length;
  }

  // Check if filters are active
  bool get hasActiveFilters {
    return _currentFilters.category != null ||
           _currentFilters.minPrice != null ||
           _currentFilters.maxPrice != null ||
           _currentFilters.condition != null ||
           _currentFilters.location != null ||
           _currentFilters.dateFrom != null ||
           _currentFilters.dateTo != null ||
           _currentFilters.hasImages == true ||
           _currentFilters.featuredOnly == true ||
           _currentFilters.availableOnly == true;
  }

  // Reset filters
  void resetFilters() {
    _currentFilters = SearchFilters();
    if (_currentQuery.isNotEmpty) {
      searchProducts(query: _currentQuery);
    }
  }

  // Update filters
  void updateFilters(SearchFilters filters) {
    _currentFilters = filters;
    if (_currentQuery.isNotEmpty) {
      searchProducts(query: _currentQuery, filters: filters);
    }
  }
}

class SearchFilters {
  String? category;
  double? minPrice;
  double? maxPrice;
  String? condition;
  String? location;
  DateTime? dateFrom;
  DateTime? dateTo;
  bool? hasImages;
  bool? featuredOnly;
  bool? availableOnly;

  SearchFilters({
    this.category,
    this.minPrice,
    this.maxPrice,
    this.condition,
    this.location,
    this.dateFrom,
    this.dateTo,
    this.hasImages,
    this.featuredOnly,
    this.availableOnly,
  });

  SearchFilters copyWith({
    String? category,
    double? minPrice,
    double? maxPrice,
    String? condition,
    String? location,
    DateTime? dateFrom,
    DateTime? dateTo,
    bool? hasImages,
    bool? featuredOnly,
    bool? availableOnly,
  }) {
    return SearchFilters(
      category: category ?? this.category,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      condition: condition ?? this.condition,
      location: location ?? this.location,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
      hasImages: hasImages ?? this.hasImages,
      featuredOnly: featuredOnly ?? this.featuredOnly,
      availableOnly: availableOnly ?? this.availableOnly,
    );
  }
}

enum SortOption {
  newest,
  oldest,
  priceLowToHigh,
  priceHighToLow,
  mostViewed,
  mostFavorited,
  alphabetical,
}

extension SortOptionExtension on SortOption {
  String get displayName {
    switch (this) {
      case SortOption.newest:
        return 'الأحدث';
      case SortOption.oldest:
        return 'الأقدم';
      case SortOption.priceLowToHigh:
        return 'السعر: من الأقل للأعلى';
      case SortOption.priceHighToLow:
        return 'السعر: من الأعلى للأقل';
      case SortOption.mostViewed:
        return 'الأكثر مشاهدة';
      case SortOption.mostFavorited:
        return 'الأكثر إعجاباً';
      case SortOption.alphabetical:
        return 'أبجدياً';
    }
  }
}
