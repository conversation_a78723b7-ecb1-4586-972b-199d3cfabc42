{"logs": [{"outputFile": "io.flutter.plugins.googlemaps.google_maps_flutter_android-mergeReleaseResources-28:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,2999", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,3095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a75f01a7707dc356bd2b3ef3fe83d0a8\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "779,884,1033,1161,1271,1425,1559,1681,1932,2105,2213,2368,2496,2657,2796,2862,2923", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "879,1028,1156,1266,1420,1554,1676,1784,2100,2208,2363,2491,2652,2791,2857,2918,2994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b763d777ce28d1ba4f435305e05f97a0\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1789", "endColumns": "142", "endOffsets": "1927"}}]}, {"outputFile": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,2999", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,3095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a75f01a7707dc356bd2b3ef3fe83d0a8\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "779,884,1033,1161,1271,1425,1559,1681,1932,2105,2213,2368,2496,2657,2796,2862,2923", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "879,1028,1156,1266,1420,1554,1676,1784,2100,2208,2363,2491,2652,2791,2857,2918,2994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b763d777ce28d1ba4f435305e05f97a0\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1789", "endColumns": "142", "endOffsets": "1927"}}]}]}