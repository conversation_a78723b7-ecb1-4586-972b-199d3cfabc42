import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/localization/app_localizations.dart';

class CategoriesSection extends StatelessWidget {
  const CategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              Text(
                localizations.categories,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // Navigate to all categories
                },
                child: Text(localizations.viewAll),
              ),
            ],
          ),
        ),
        Sized<PERSON>ox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            itemCount: _getCategories(localizations).length,
            itemBuilder: (context, index) {
              final category = _getCategories(localizations)[index];
              return _buildCategoryItem(context, category);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryItem(BuildContext context, CategoryItem category) {
    return GestureDetector(
      onTap: () {
        // Navigate to category products
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: category.gradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: category.color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                category.icon,
                color: AppColors.white,
                size: 28,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  List<CategoryItem> _getCategories(AppLocalizations localizations) {
    return [
      CategoryItem(
        name: localizations.cars,
        icon: Icons.directions_car,
        color: AppColors.categoryAutos,
        gradient: LinearGradient(
          colors: [AppColors.categoryAutos, AppColors.categoryAutos.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.realEstate,
        icon: Icons.home,
        color: AppColors.categoryRealEstate,
        gradient: LinearGradient(
          colors: [AppColors.categoryRealEstate, AppColors.categoryRealEstate.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.electronics,
        icon: Icons.smartphone,
        color: AppColors.categoryElectronics,
        gradient: LinearGradient(
          colors: [AppColors.categoryElectronics, AppColors.categoryElectronics.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.fashion,
        icon: Icons.checkroom,
        color: AppColors.categoryFashion,
        gradient: LinearGradient(
          colors: [AppColors.categoryFashion, AppColors.categoryFashion.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.jobs,
        icon: Icons.work,
        color: AppColors.categoryJobs,
        gradient: LinearGradient(
          colors: [AppColors.categoryJobs, AppColors.categoryJobs.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.services,
        icon: Icons.build,
        color: AppColors.categoryServices,
        gradient: LinearGradient(
          colors: [AppColors.categoryServices, AppColors.categoryServices.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.sports,
        icon: Icons.sports_soccer,
        color: AppColors.success,
        gradient: LinearGradient(
          colors: [AppColors.success, AppColors.success.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      CategoryItem(
        name: localizations.books,
        icon: Icons.menu_book,
        color: AppColors.info,
        gradient: LinearGradient(
          colors: [AppColors.info, AppColors.info.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
    ];
  }
}

class CategoryItem {
  final String name;
  final IconData icon;
  final Color color;
  final LinearGradient gradient;

  CategoryItem({
    required this.name,
    required this.icon,
    required this.color,
    required this.gradient,
  });
}
