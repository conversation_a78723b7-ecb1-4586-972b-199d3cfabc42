enum UserType { buyer, seller, admin }
enum AccountStatus { active, suspended, pending, deleted }

class UserModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? profileImage;
  final String? bio;
  final String? location;
  final String? address;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLoginAt;
  final bool isVerified;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final UserType userType;
  final AccountStatus status;
  final double rating;
  final int reviewsCount;
  final int totalSales;
  final int totalPurchases;
  final List<String> favoriteCategories;
  final UserSettings settings;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.profileImage,
    this.bio,
    this.location,
    this.address,
    required this.createdAt,
    this.updatedAt,
    this.lastLoginAt,
    this.isVerified = false,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.userType = UserType.buyer,
    this.status = AccountStatus.active,
    this.rating = 0.0,
    this.reviewsCount = 0,
    this.totalSales = 0,
    this.totalPurchases = 0,
    this.favoriteCategories = const [],
    required this.settings,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      profileImage: json['profile_image'],
      bio: json['bio'],
      location: json['location'],
      address: json['address'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      lastLoginAt: json['last_login_at'] != null ? DateTime.parse(json['last_login_at']) : null,
      isVerified: json['is_verified'] ?? false,
      isEmailVerified: json['is_email_verified'] ?? false,
      isPhoneVerified: json['is_phone_verified'] ?? false,
      userType: UserType.values.firstWhere(
        (e) => e.toString() == 'UserType.${json['user_type']}',
        orElse: () => UserType.buyer,
      ),
      status: AccountStatus.values.firstWhere(
        (e) => e.toString() == 'AccountStatus.${json['status']}',
        orElse: () => AccountStatus.active,
      ),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewsCount: json['reviews_count'] ?? 0,
      totalSales: json['total_sales'] ?? 0,
      totalPurchases: json['total_purchases'] ?? 0,
      favoriteCategories: List<String>.from(json['favorite_categories'] ?? []),
      settings: UserSettings.fromJson(json['settings'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'profile_image': profileImage,
      'bio': bio,
      'location': location,
      'address': address,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'is_verified': isVerified,
      'is_email_verified': isEmailVerified,
      'is_phone_verified': isPhoneVerified,
      'user_type': userType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'rating': rating,
      'reviews_count': reviewsCount,
      'total_sales': totalSales,
      'total_purchases': totalPurchases,
      'favorite_categories': favoriteCategories,
      'settings': settings.toJson(),
    };
  }

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profileImage,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    double? rating,
    int? reviewsCount,
    List<String>? favoriteCategories,
    UserSettings? settings,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      favoriteCategories: favoriteCategories ?? this.favoriteCategories,
      settings: settings ?? this.settings,
    );
  }

  // Helper methods
  bool get isSeller => userType == UserType.seller || userType == UserType.admin;
  bool get isAdmin => userType == UserType.admin;
  bool get isActive => status == AccountStatus.active;
  bool get isFullyVerified => isEmailVerified && isPhoneVerified;

  String get displayName => name.isNotEmpty ? name : email.split('@').first;

  String get ratingText {
    if (reviewsCount == 0) return 'لا توجد تقييمات';
    return '${rating.toStringAsFixed(1)} ($reviewsCount تقييم)';
  }

  String get statusText {
    switch (status) {
      case AccountStatus.active:
        return 'نشط';
      case AccountStatus.suspended:
        return 'معلق';
      case AccountStatus.pending:
        return 'في الانتظار';
      case AccountStatus.deleted:
        return 'محذوف';
    }
  }

  String get userTypeText {
    switch (userType) {
      case UserType.buyer:
        return 'مشتري';
      case UserType.seller:
        return 'بائع';
      case UserType.admin:
        return 'مدير';
    }
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, type: $userType, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class UserSettings {
  final bool notificationsEnabled;
  final bool darkModeEnabled;
  final String language;
  final bool locationEnabled;
  final bool chatEnabled;
  final String currency;
  final List<String> blockedUsers;

  UserSettings({
    this.notificationsEnabled = true,
    this.darkModeEnabled = false,
    this.language = 'ar',
    this.locationEnabled = true,
    this.chatEnabled = true,
    this.currency = 'SAR',
    this.blockedUsers = const [],
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      notificationsEnabled: json['notifications_enabled'] ?? true,
      darkModeEnabled: json['dark_mode_enabled'] ?? false,
      language: json['language'] ?? 'ar',
      locationEnabled: json['location_enabled'] ?? true,
      chatEnabled: json['chat_enabled'] ?? true,
      currency: json['currency'] ?? 'SAR',
      blockedUsers: List<String>.from(json['blocked_users'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notifications_enabled': notificationsEnabled,
      'dark_mode_enabled': darkModeEnabled,
      'language': language,
      'location_enabled': locationEnabled,
      'chat_enabled': chatEnabled,
      'currency': currency,
      'blocked_users': blockedUsers,
    };
  }

  UserSettings copyWith({
    bool? notificationsEnabled,
    bool? darkModeEnabled,
    String? language,
    bool? locationEnabled,
    bool? chatEnabled,
    String? currency,
    List<String>? blockedUsers,
  }) {
    return UserSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      language: language ?? this.language,
      locationEnabled: locationEnabled ?? this.locationEnabled,
      chatEnabled: chatEnabled ?? this.chatEnabled,
      currency: currency ?? this.currency,
      blockedUsers: blockedUsers ?? this.blockedUsers,
    );
  }
}
