<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="منصة تجارة إلكترونية شاملة للبيع والشراء">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="يو سيل - USale">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>يو سيل - USale</title>
  <link rel="manifest" href="manifest.json">

  <!-- Firebase SDKs -->
  <script type="module">
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';

    const firebaseConfig = {
      apiKey: "AIzaSyABCDEFGHIJKLMNOPQRSTUVWXYZ1234567",
      authDomain: "usale-marketplace.firebaseapp.com",
      projectId: "usale-marketplace",
      storageBucket: "usale-marketplace.appspot.com",
      messagingSenderId: "123456789012",
      appId: "1:123456789012:web:abcdef1234567890",
      measurementId: "G-ABCDEFGHIJ"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    window.firebaseApp = app;
  </script>
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
