import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/location_model.dart';

class LocationService extends ChangeNotifier {
  // Singleton pattern
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  LocationModel? _currentLocation;
  List<LocationModel> _savedLocations = [];
  List<LocationModel> _nearbyProducts = [];
  bool _isLoading = false;
  bool _locationPermissionGranted = false;

  // Getters
  LocationModel? get currentLocation => _currentLocation;
  List<LocationModel> get savedLocations => _savedLocations;
  List<LocationModel> get nearbyProducts => _nearbyProducts;
  bool get isLoading => _isLoading;
  bool get locationPermissionGranted => _locationPermissionGranted;

  // Initialize location service
  Future<void> initialize() async {
    await requestLocationPermission();
    if (_locationPermissionGranted) {
      await getCurrentLocation();
      await loadSavedLocations();
    }
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    try {
      // In real app, use location package
      // For now, simulate permission granted
      await Future.delayed(const Duration(milliseconds: 500));
      _locationPermissionGranted = true;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      return false;
    }
  }

  // Get current location
  Future<LocationResult> getCurrentLocation() async {
    if (!_locationPermissionGranted) {
      return LocationResult.error('لم يتم منح إذن الموقع');
    }

    _isLoading = true;
    notifyListeners();

    try {
      // In real app, use location package
      // For now, simulate getting location
      await Future.delayed(const Duration(seconds: 2));
      
      _currentLocation = LocationModel(
        id: 'current',
        name: 'موقعي الحالي',
        address: 'الرياض، المملكة العربية السعودية',
        latitude: 24.7136,
        longitude: 46.6753,
        city: 'الرياض',
        country: 'المملكة العربية السعودية',
        type: LocationType.current,
        createdAt: DateTime.now(),
      );

      notifyListeners();
      return LocationResult.success('تم تحديد الموقع بنجاح', _currentLocation!);
    } catch (e) {
      debugPrint('Error getting current location: $e');
      return LocationResult.error('فشل في تحديد الموقع');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search locations
  Future<List<LocationModel>> searchLocations(String query) async {
    if (query.isEmpty) return [];

    try {
      // In real app, use Google Places API
      await Future.delayed(const Duration(milliseconds: 800));
      
      return [
        LocationModel(
          id: '1',
          name: 'مول الرياض غاليري',
          address: 'طريق الملك فهد، الرياض',
          latitude: 24.7136,
          longitude: 46.6753,
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          type: LocationType.mall,
          createdAt: DateTime.now(),
        ),
        LocationModel(
          id: '2',
          name: 'جامعة الملك سعود',
          address: 'الدرعية، الرياض',
          latitude: 24.7236,
          longitude: 46.6253,
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          type: LocationType.university,
          createdAt: DateTime.now(),
        ),
      ];
    } catch (e) {
      debugPrint('Error searching locations: $e');
      return [];
    }
  }

  // Save location
  Future<LocationResult> saveLocation(LocationModel location) async {
    try {
      // Check if location already saved
      if (_savedLocations.any((loc) => loc.id == location.id)) {
        return LocationResult.error('الموقع محفوظ بالفعل');
      }

      final savedLocation = location.copyWith(
        type: LocationType.saved,
        createdAt: DateTime.now(),
      );

      _savedLocations.add(savedLocation);
      notifyListeners();

      return LocationResult.success('تم حفظ الموقع بنجاح', savedLocation);
    } catch (e) {
      debugPrint('Error saving location: $e');
      return LocationResult.error('فشل في حفظ الموقع');
    }
  }

  // Remove saved location
  Future<bool> removeSavedLocation(String locationId) async {
    try {
      _savedLocations.removeWhere((loc) => loc.id == locationId);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error removing saved location: $e');
      return false;
    }
  }

  // Load saved locations
  Future<void> loadSavedLocations() async {
    try {
      // In real app, load from database
      _savedLocations = [
        LocationModel(
          id: 'home',
          name: 'المنزل',
          address: 'حي النرجس، الرياض',
          latitude: 24.7500,
          longitude: 46.6500,
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          type: LocationType.home,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
        ),
        LocationModel(
          id: 'work',
          name: 'العمل',
          address: 'حي العليا، الرياض',
          latitude: 24.7000,
          longitude: 46.6800,
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          type: LocationType.work,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
      ];
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading saved locations: $e');
    }
  }

  // Find nearby products
  Future<void> findNearbyProducts({double radiusKm = 10.0}) async {
    if (_currentLocation == null) {
      await getCurrentLocation();
    }

    if (_currentLocation == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // In real app, query database with location
      await Future.delayed(const Duration(seconds: 1));
      
      _nearbyProducts = [
        LocationModel(
          id: 'product1',
          name: 'آيفون 15 برو ماكس',
          address: 'حي الملز، الرياض',
          latitude: 24.7200,
          longitude: 46.6600,
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          type: LocationType.product,
          productId: '1',
          distance: 2.5,
          createdAt: DateTime.now(),
        ),
        LocationModel(
          id: 'product2',
          name: 'سيارة تويوتا كامري',
          address: 'حي الصحافة، الرياض',
          latitude: 24.7400,
          longitude: 46.6400,
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          type: LocationType.product,
          productId: '2',
          distance: 5.8,
          createdAt: DateTime.now(),
        ),
      ];

      notifyListeners();
    } catch (e) {
      debugPrint('Error finding nearby products: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Calculate distance between two points
  double calculateDistance(
    double lat1, double lon1,
    double lat2, double lon2,
  ) {
    // Simplified distance calculation
    // In real app, use proper haversine formula
    const double earthRadius = 6371; // km
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = 
        (dLat / 2) * (dLat / 2) +
        (dLon / 2) * (dLon / 2) * 
        _cos(_degreesToRadians(lat1)) * 
        _cos(_degreesToRadians(lat2));
    
    final double c = 2 * _atan2(_sqrt(a), _sqrt(1 - a));
    
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }

  double _cos(double radians) {
    // Simplified cos calculation
    return 1 - (radians * radians) / 2;
  }

  // double _sin(double radians) { // Removed unused method
  //   // Simplified sin calculation
  //   return radians - (radians * radians * radians) / 6;
  // }

  double _sqrt(double value) {
    // Simplified sqrt calculation
    double x = value;
    double y = 1;
    double e = 0.000001;
    
    while (x - y > e) {
      x = (x + y) / 2;
      y = value / x;
    }
    
    return x;
  }

  double _atan2(double y, double x) {
    // Simplified atan2 calculation
    if (x > 0) return _atan(y / x);
    if (x < 0 && y >= 0) return _atan(y / x) + 3.14159265359;
    if (x < 0 && y < 0) return _atan(y / x) - 3.14159265359;
    if (x == 0 && y > 0) return 3.14159265359 / 2;
    if (x == 0 && y < 0) return -3.14159265359 / 2;
    return 0;
  }

  double _atan(double x) {
    // Simplified atan calculation
    return x - (x * x * x) / 3 + (x * x * x * x * x) / 5;
  }

  // Get location suggestions based on user history
  List<LocationModel> getLocationSuggestions() {
    final suggestions = <LocationModel>[];
    
    // Add current location if available
    if (_currentLocation != null) {
      suggestions.add(_currentLocation!);
    }
    
    // Add saved locations
    suggestions.addAll(_savedLocations);
    
    return suggestions;
  }

  // Clear cache
  void clearCache() {
    _nearbyProducts.clear();
    notifyListeners();
  }
}

class LocationResult {
  final bool isSuccess;
  final String message;
  final LocationModel? location;

  LocationResult._(this.isSuccess, this.message, [this.location]);

  factory LocationResult.success(String message, [LocationModel? location]) {
    return LocationResult._(true, message, location);
  }

  factory LocationResult.error(String message) {
    return LocationResult._(false, message);
  }
}
