import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../services/firebase_auth_service.dart';
import '../../presentation/pages/login_page.dart';
import '../../../home/<USER>/pages/main_page.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<FirebaseAuthService>(
      builder: (context, authService, child) {
        // Show loading while checking auth state
        if (authService.isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Show main app if authenticated, login page if not
        if (authService.isAuthenticated) {
          return const MainPage();
        } else {
          return const LoginPage();
        }
      },
    );
  }
}
