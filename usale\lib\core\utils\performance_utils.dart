import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class PerformanceUtils {
  // Singleton pattern
  static final PerformanceUtils _instance = PerformanceUtils._internal();
  factory PerformanceUtils() => _instance;
  PerformanceUtils._internal();

  // Performance monitoring
  static void logPerformance(String operation, Duration duration) {
    if (kDebugMode) {
      print('Performance: $operation took ${duration.inMilliseconds}ms');
    }
  }

  // Measure execution time
  static Future<T> measureAsync<T>(
    String operation,
    Future<T> Function() function,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      logPerformance(operation, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      logPerformance('$operation (failed)', stopwatch.elapsed);
      rethrow;
    }
  }

  // Measure synchronous execution time
  static T measureSync<T>(
    String operation,
    T Function() function,
  ) {
    final stopwatch = Stopwatch()..start();
    try {
      final result = function();
      stopwatch.stop();
      logPerformance(operation, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      logPerformance('$operation (failed)', stopwatch.elapsed);
      rethrow;
    }
  }

  // Debounce function calls
  static Function debounce(
    Function function,
    Duration delay,
  ) {
    Timer? timer;
    return () {
      timer?.cancel();
      timer = Timer(delay, () => function());
    };
  }

  // Throttle function calls
  static Function throttle(
    Function function,
    Duration duration,
  ) {
    bool isThrottled = false;
    return () {
      if (!isThrottled) {
        function();
        isThrottled = true;
        Timer(duration, () => isThrottled = false);
      }
    };
  }

  // Memory usage monitoring
  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      // This is a placeholder - in a real app you might use
      // platform-specific memory monitoring
      print('Memory check: $context');
    }
  }

  // Image optimization helpers
  static ImageProvider optimizeImage(
    String imageUrl, {
    int? width,
    int? height,
    int quality = 80,
  }) {
    // In a real app, you might use a service like Cloudinary
    // or implement your own image optimization
    return NetworkImage(imageUrl);
  }

  // List optimization
  static Widget buildOptimizedList({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      controller: controller,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      // Add performance optimizations
      cacheExtent: 500, // Cache items outside viewport
      addAutomaticKeepAlives: false, // Don't keep items alive automatically
      addRepaintBoundaries: false, // Reduce repaint boundaries for simple items
    );
  }

  // Lazy loading helper
  static Widget buildLazyLoadingList({
    required List<dynamic> items,
    required Widget Function(BuildContext, dynamic, int) itemBuilder,
    required Future<void> Function() onLoadMore,
    bool hasMore = true,
    bool isLoading = false,
  }) {
    return ListView.builder(
      itemCount: items.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == items.length) {
          // Load more indicator
          if (!isLoading) {
            // Trigger load more
            WidgetsBinding.instance.addPostFrameCallback((_) {
              onLoadMore();
            });
          }
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
          );
        }
        return itemBuilder(context, items[index], index);
      },
    );
  }

  // Image caching helper
  static void precacheImages(
    BuildContext context,
    List<String> imageUrls,
  ) {
    for (final url in imageUrls) {
      precacheImage(NetworkImage(url), context);
    }
  }

  // Widget caching
  static final Map<String, Widget> _widgetCache = {};

  static Widget getCachedWidget(
    String key,
    Widget Function() builder,
  ) {
    if (_widgetCache.containsKey(key)) {
      return _widgetCache[key]!;
    }
    final widget = builder();
    _widgetCache[key] = widget;
    return widget;
  }

  static void clearWidgetCache() {
    _widgetCache.clear();
  }

  // Animation optimization
  static AnimationController createOptimizedAnimationController({
    required Duration duration,
    required TickerProvider vsync,
    double? value,
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
      value: value,
    );
  }

  // Network request optimization
  static Future<T> optimizedNetworkRequest<T>(
    Future<T> Function() request, {
    Duration timeout = const Duration(seconds: 30),
    int retries = 3,
  }) async {
    for (int i = 0; i < retries; i++) {
      try {
        return await request().timeout(timeout);
      } catch (e) {
        if (i == retries - 1) rethrow;
        await Future.delayed(Duration(seconds: i + 1));
      }
    }
    throw Exception('Request failed after $retries retries');
  }

  // State management optimization
  static bool shouldRebuild<T>(T oldValue, T newValue) {
    return oldValue != newValue;
  }

  // Scroll performance optimization
  static ScrollPhysics get optimizedScrollPhysics {
    return const BouncingScrollPhysics(
      parent: AlwaysScrollableScrollPhysics(),
    );
  }

  // Build context optimization
  static void schedulePostFrame(VoidCallback callback) {
    WidgetsBinding.instance.addPostFrameCallback((_) => callback());
  }

  // Memory cleanup
  static void cleanup() {
    clearWidgetCache();
    // Add other cleanup operations as needed
  }
}

// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final String name;

  const PerformanceMonitor({
    super.key,
    required this.child,
    required this.name,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  late Stopwatch _stopwatch;

  @override
  void initState() {
    super.initState();
    _stopwatch = Stopwatch()..start();
  }

  @override
  void dispose() {
    _stopwatch.stop();
    PerformanceUtils.logPerformance(
      'Widget ${widget.name} lifecycle',
      _stopwatch.elapsed,
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

// Optimized image widget
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            const Icon(
              Icons.error,
              color: Colors.red,
            );
      },
      // Performance optimizations
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
    );
  }
}
