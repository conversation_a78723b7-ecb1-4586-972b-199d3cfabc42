import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';
import '../models/security_model.dart';
import '../services/auth_service.dart';

class SecurityService extends ChangeNotifier {
  // Singleton pattern
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  final AuthService _authService = AuthService();

  List<SecurityLog> _securityLogs = [];
  List<LoginAttempt> _loginAttempts = [];
  SecuritySettings _securitySettings = SecuritySettings();
  final bool _isSecurityCheckEnabled = true;
  Timer? _securityTimer;

  // Getters
  List<SecurityLog> get securityLogs => _securityLogs;
  List<LoginAttempt> get loginAttempts => _loginAttempts;
  SecuritySettings get securitySettings => _securitySettings;
  bool get isSecurityCheckEnabled => _isSecurityCheckEnabled;

  // Initialize security service
  Future<void> initialize() async {
    await loadSecuritySettings();
    await loadSecurityLogs();
    _startSecurityMonitoring();
  }

  // Hash password
  String hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Generate salt
  String generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  // Validate password strength
  PasswordStrength validatePasswordStrength(String password) {
    int score = 0;
    List<String> issues = [];

    // Length check
    if (password.length >= 8) {
      score += 1;
    } else {
      issues.add('يجب أن تكون كلمة المرور 8 أحرف على الأقل');
    }

    // Uppercase check
    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على حرف كبير واحد على الأقل');
    }

    // Lowercase check
    if (password.contains(RegExp(r'[a-z]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على حرف صغير واحد على الأقل');
    }

    // Number check
    if (password.contains(RegExp(r'[0-9]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على رقم واحد على الأقل');
    }

    // Special character check
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على رمز خاص واحد على الأقل');
    }

    // Common password check
    if (_isCommonPassword(password)) {
      score -= 2;
      issues.add('كلمة المرور شائعة جداً');
    }

    PasswordStrengthLevel level;
    if (score >= 4) {
      level = PasswordStrengthLevel.strong;
    } else if (score >= 3) {
      level = PasswordStrengthLevel.medium;
    } else if (score >= 2) {
      level = PasswordStrengthLevel.weak;
    } else {
      level = PasswordStrengthLevel.veryWeak;
    }

    return PasswordStrength(
      level: level,
      score: score,
      issues: issues,
    );
  }

  // Check if password is common
  bool _isCommonPassword(String password) {
    final commonPasswords = [
      '123456', 'password', '123456789', '12345678', '12345',
      '1234567', '1234567890', 'qwerty', 'abc123', '111111',
      '123123', 'admin', 'letmein', 'welcome', 'monkey',
    ];
    return commonPasswords.contains(password.toLowerCase());
  }

  // Log security event
  Future<void> logSecurityEvent({
    required SecurityEventType type,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    final currentUser = _authService.currentUser;
    
    try {
      final log = SecurityLog(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser?.id,
        eventType: type,
        description: description,
        ipAddress: await _getCurrentIpAddress(),
        userAgent: await _getUserAgent(),
        metadata: metadata ?? {},
        timestamp: DateTime.now(),
      );

      _securityLogs.insert(0, log);
      
      // Keep only last 1000 logs
      if (_securityLogs.length > 1000) {
        _securityLogs = _securityLogs.take(1000).toList();
      }

      notifyListeners();

      // Check for suspicious activity
      await _checkSuspiciousActivity(log);
    } catch (e) {
      debugPrint('Error logging security event: $e');
    }
  }

  // Track login attempt
  Future<void> trackLoginAttempt({
    required String email,
    required bool isSuccessful,
    String? failureReason,
  }) async {
    try {
      final attempt = LoginAttempt(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        isSuccessful: isSuccessful,
        failureReason: failureReason,
        ipAddress: await _getCurrentIpAddress(),
        userAgent: await _getUserAgent(),
        timestamp: DateTime.now(),
      );

      _loginAttempts.insert(0, attempt);
      
      // Keep only last 500 attempts
      if (_loginAttempts.length > 500) {
        _loginAttempts = _loginAttempts.take(500).toList();
      }

      notifyListeners();

      // Log security event
      await logSecurityEvent(
        type: isSuccessful 
            ? SecurityEventType.loginSuccess 
            : SecurityEventType.loginFailure,
        description: isSuccessful 
            ? 'تسجيل دخول ناجح' 
            : 'فشل في تسجيل الدخول: $failureReason',
        metadata: {'email': email},
      );

      // Check for brute force attacks
      if (!isSuccessful) {
        await _checkBruteForceAttack(email);
      }
    } catch (e) {
      debugPrint('Error tracking login attempt: $e');
    }
  }

  // Check for brute force attacks
  Future<void> _checkBruteForceAttack(String email) async {
    final now = DateTime.now();
    final lastHour = now.subtract(const Duration(hours: 1));
    
    final recentFailures = _loginAttempts.where((attempt) =>
        attempt.email == email &&
        !attempt.isSuccessful &&
        attempt.timestamp.isAfter(lastHour)
    ).length;

    if (recentFailures >= 5) {
      await logSecurityEvent(
        type: SecurityEventType.bruteForceDetected,
        description: 'تم اكتشاف محاولة هجوم القوة الغاشمة',
        metadata: {
          'email': email,
          'failed_attempts': recentFailures,
        },
      );

      // In real app, implement account lockout
      debugPrint('Brute force attack detected for email: $email');
    }
  }

  // Check for suspicious activity
  Future<void> _checkSuspiciousActivity(SecurityLog log) async {
    // Check for multiple failed logins
    if (log.eventType == SecurityEventType.loginFailure) {
      final recentFailures = _securityLogs.where((l) =>
          l.eventType == SecurityEventType.loginFailure &&
          l.timestamp.isAfter(DateTime.now().subtract(const Duration(minutes: 30)))
      ).length;

      if (recentFailures >= 3) {
        await logSecurityEvent(
          type: SecurityEventType.suspiciousActivity,
          description: 'نشاط مشبوه: محاولات دخول متعددة فاشلة',
        );
      }
    }

    // Check for unusual IP addresses
    if (log.ipAddress != null) {
      final recentIps = _securityLogs
          .where((l) => l.timestamp.isAfter(DateTime.now().subtract(const Duration(days: 7))))
          .map((l) => l.ipAddress)
          .where((ip) => ip != null)
          .toSet();

      if (recentIps.length > 5) {
        await logSecurityEvent(
          type: SecurityEventType.suspiciousActivity,
          description: 'نشاط مشبوه: عناوين IP متعددة',
        );
      }
    }
  }

  // Enable two-factor authentication
  Future<TwoFactorResult> enableTwoFactor() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return TwoFactorResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Generate secret key
      final secretKey = _generateSecretKey();
      
      // Generate QR code data
      final qrCodeData = _generateQRCodeData(currentUser.email, secretKey);
      
      // Generate backup codes
      final backupCodes = _generateBackupCodes();

      _securitySettings = _securitySettings.copyWith(
        twoFactorEnabled: true,
        twoFactorSecret: secretKey,
        backupCodes: backupCodes,
      );

      await logSecurityEvent(
        type: SecurityEventType.twoFactorEnabled,
        description: 'تم تفعيل المصادقة الثنائية',
      );

      notifyListeners();

      return TwoFactorResult.success(
        'تم تفعيل المصادقة الثنائية بنجاح',
        qrCodeData,
        backupCodes,
      );
    } catch (e) {
      debugPrint('Error enabling two-factor authentication: $e');
      return TwoFactorResult.error('فشل في تفعيل المصادقة الثنائية');
    }
  }

  // Verify two-factor code
  bool verifyTwoFactorCode(String code) {
    if (!_securitySettings.twoFactorEnabled || 
        _securitySettings.twoFactorSecret == null) {
      return false;
    }

    // In real app, use TOTP algorithm
    // For now, simulate verification
    return code.length == 6 && code.contains(RegExp(r'^[0-9]+$'));
  }

  // Generate secret key for 2FA
  String _generateSecretKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(20, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  // Generate QR code data
  String _generateQRCodeData(String email, String secretKey) {
    return 'otpauth://totp/USale:$email?secret=$secretKey&issuer=USale';
  }

  // Generate backup codes
  List<String> _generateBackupCodes() {
    final random = Random.secure();
    return List.generate(10, (index) {
      return List.generate(8, (i) => random.nextInt(10)).join();
    });
  }

  // Get current IP address
  Future<String?> _getCurrentIpAddress() async {
    // In real app, get actual IP address
    return '*************';
  }

  // Get user agent
  Future<String?> _getUserAgent() async {
    // In real app, get actual user agent
    return 'USale Mobile App';
  }

  // Load security settings
  Future<void> loadSecuritySettings() async {
    try {
      // In real app, load from secure storage
      _securitySettings = SecuritySettings(
        twoFactorEnabled: false,
        sessionTimeout: const Duration(hours: 24),
        passwordExpiryDays: 90,
        maxLoginAttempts: 5,
        requireStrongPassword: true,
      );
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading security settings: $e');
    }
  }

  // Load security logs
  Future<void> loadSecurityLogs() async {
    try {
      // In real app, load from database
      _securityLogs = [];
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading security logs: $e');
    }
  }

  // Start security monitoring
  void _startSecurityMonitoring() {
    _securityTimer?.cancel();
    _securityTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _performSecurityCheck();
    });
  }

  // Perform security check
  void _performSecurityCheck() {
    if (!_isSecurityCheckEnabled) return;

    // Check for expired sessions
    _checkExpiredSessions();
    
    // Check for suspicious patterns
    _checkSuspiciousPatterns();
  }

  // Check for expired sessions
  void _checkExpiredSessions() {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    // In real app, check session expiry
    // For now, just log the check
    debugPrint('Security check: Session validation');
  }

  // Check for suspicious patterns
  void _checkSuspiciousPatterns() {
    final now = DateTime.now();
    final lastHour = now.subtract(const Duration(hours: 1));
    
    final recentEvents = _securityLogs.where((log) =>
        log.timestamp.isAfter(lastHour)
    ).length;

    if (recentEvents > 100) {
      logSecurityEvent(
        type: SecurityEventType.suspiciousActivity,
        description: 'نشاط مشبوه: عدد كبير من الأحداث الأمنية',
        metadata: {'events_count': recentEvents},
      );
    }
  }

  @override
  void dispose() {
    _securityTimer?.cancel();
    super.dispose();
  }
}
