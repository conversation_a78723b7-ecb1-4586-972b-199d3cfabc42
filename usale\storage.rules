rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /users/{userId}/profile/{fileName} {
      // Only authenticated users can upload their own profile images
      allow read: if true; // Profile images are public
      allow write: if request.auth != null && request.auth.uid == userId
        && isValidImageFile()
        && request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // Product images
    match /products/{productId}/{fileName} {
      // Anyone can read product images
      allow read: if true;
      
      // Only authenticated users can upload product images
      allow write: if request.auth != null
        && isValidImageFile()
        && request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // Chat attachments
    match /chats/{chatId}/attachments/{fileName} {
      // Only chat participants can read/write attachments
      allow read, write: if request.auth != null
        && isValidFile()
        && request.resource.size < 20 * 1024 * 1024; // 20MB limit
    }
    
    // Review images
    match /reviews/{reviewId}/images/{fileName} {
      // Anyone can read review images
      allow read: if true;
      
      // Only authenticated users can upload review images
      allow write: if request.auth != null
        && isValidImageFile()
        && request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // Temporary uploads
    match /temp/{userId}/{fileName} {
      // Only authenticated users can upload to their temp folder
      allow read, write: if request.auth != null && request.auth.uid == userId
        && request.resource.size < 50 * 1024 * 1024; // 50MB limit
        
      // Auto-delete after 24 hours (implement with Cloud Functions)
    }
    
    // Helper functions
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*')
        && request.resource.contentType in ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    }
    
    function isValidFile() {
      return request.resource.contentType.matches('image/.*')
        || request.resource.contentType.matches('video/.*')
        || request.resource.contentType.matches('audio/.*')
        || request.resource.contentType in [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain'
        ];
    }
  }
}
