import 'package:flutter/material.dart';
import 'dart:math';

/// خدمة التوصيات الذكية
class SmartRecommendationsService extends ChangeNotifier {
  static final SmartRecommendationsService _instance = SmartRecommendationsService._internal();
  factory SmartRecommendationsService() => _instance;
  SmartRecommendationsService._internal();

  // بيانات المستخدم والتوصيات
  UserProfile _userProfile = UserProfile();
  List<Recommendation> _recommendations = [];
  Map<String, double> _categoryPreferences = {};
  Map<String, int> _viewHistory = {};
  Map<String, int> _searchHistory = {};
  List<String> _favoriteItems = [];
  bool _isLearning = true;

  // محرك التعلم الآلي
  final MLRecommendationEngine _mlEngine = MLRecommendationEngine();

  // Getters
  List<Recommendation> get recommendations => _recommendations;
  UserProfile get userProfile => _userProfile;
  Map<String, double> get categoryPreferences => _categoryPreferences;
  bool get isLearning => _isLearning;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadUserData();
    await _generateInitialRecommendations();
    _startLearning();
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserData() async {
    // محاكاة تحميل البيانات
    _userProfile = UserProfile(
      age: 28,
      gender: 'male',
      location: 'الرياض',
      interests: ['تقنية', 'سيارات', 'رياضة'],
      priceRange: PriceRange(min: 100, max: 5000),
    );

    _categoryPreferences = {
      'إلكترونيات': 0.8,
      'مركبات': 0.6,
      'رياضة': 0.7,
      'أزياء': 0.3,
      'عقارات': 0.4,
    };

    _viewHistory = {
      'آيفون 15': 5,
      'لابتوب ديل': 3,
      'تويوتا كامري': 2,
      'ساعة أبل': 4,
    };
  }

  /// إنشاء التوصيات الأولية
  Future<void> _generateInitialRecommendations() async {
    _recommendations = await _mlEngine.generateRecommendations(_userProfile, _categoryPreferences);
    notifyListeners();
  }

  /// بدء عملية التعلم
  void _startLearning() {
    _isLearning = true;
    // يمكن إضافة مؤقت للتحديث الدوري
  }

  /// تسجيل عرض منتج
  void recordProductView(String productId, String category) {
    _viewHistory[productId] = (_viewHistory[productId] ?? 0) + 1;
    _updateCategoryPreference(category, 0.1);
    _updateRecommendations();
  }

  /// تسجيل البحث
  void recordSearch(String query, String category) {
    _searchHistory[query] = (_searchHistory[query] ?? 0) + 1;
    _updateCategoryPreference(category, 0.05);
    _updateRecommendations();
  }

  /// تسجيل إضافة للمفضلة
  void recordFavorite(String productId, String category) {
    if (!_favoriteItems.contains(productId)) {
      _favoriteItems.add(productId);
      _updateCategoryPreference(category, 0.2);
      _updateRecommendations();
    }
  }

  /// تسجيل شراء
  void recordPurchase(String productId, String category, double price) {
    _updateCategoryPreference(category, 0.3);
    _updatePricePreference(price);
    _updateRecommendations();
  }

  /// تحديث تفضيلات الفئة
  void _updateCategoryPreference(String category, double increment) {
    _categoryPreferences[category] = 
        (_categoryPreferences[category] ?? 0.0) + increment;
    
    // تطبيع القيم
    _normalizeCategoryPreferences();
  }

  /// تطبيع تفضيلات الفئات
  void _normalizeCategoryPreferences() {
    final maxValue = _categoryPreferences.values.reduce(max);
    if (maxValue > 1.0) {
      _categoryPreferences.updateAll((key, value) => value / maxValue);
    }
  }

  /// تحديث تفضيلات السعر
  void _updatePricePreference(double price) {
    // تحديث نطاق السعر المفضل
    if (price < _userProfile.priceRange.min) {
      _userProfile.priceRange = PriceRange(
        min: price * 0.8,
        max: _userProfile.priceRange.max,
      );
    } else if (price > _userProfile.priceRange.max) {
      _userProfile.priceRange = PriceRange(
        min: _userProfile.priceRange.min,
        max: price * 1.2,
      );
    }
  }

  /// تحديث التوصيات
  Future<void> _updateRecommendations() async {
    if (_isLearning) {
      _recommendations = await _mlEngine.generateRecommendations(
        _userProfile, 
        _categoryPreferences,
        viewHistory: _viewHistory,
        searchHistory: _searchHistory,
        favoriteItems: _favoriteItems,
      );
      notifyListeners();
    }
  }

  /// الحصول على توصيات لفئة معينة
  List<Recommendation> getRecommendationsForCategory(String category) {
    return _recommendations
        .where((rec) => rec.category == category)
        .take(5)
        .toList();
  }

  /// الحصول على توصيات مشابهة لمنتج
  Future<List<Recommendation>> getSimilarProducts(String productId) async {
    return await _mlEngine.findSimilarProducts(productId, _recommendations);
  }

  /// الحصول على توصيات شخصية
  List<Recommendation> getPersonalizedRecommendations() {
    return _recommendations
        .where((rec) => rec.isPersonalized)
        .take(10)
        .toList();
  }

  /// الحصول على العروض المقترحة
  List<Recommendation> getTrendingRecommendations() {
    return _recommendations
        .where((rec) => rec.isTrending)
        .take(8)
        .toList();
  }

  /// تفعيل/إلغاء تفعيل التعلم
  void toggleLearning(bool enabled) {
    _isLearning = enabled;
    notifyListeners();
  }

  /// إعادة تعيين التوصيات
  Future<void> resetRecommendations() async {
    _categoryPreferences.clear();
    _viewHistory.clear();
    _searchHistory.clear();
    _favoriteItems.clear();
    await _generateInitialRecommendations();
  }
}

/// محرك التعلم الآلي للتوصيات
class MLRecommendationEngine {
  /// إنشاء التوصيات
  Future<List<Recommendation>> generateRecommendations(
    UserProfile profile,
    Map<String, double> categoryPreferences, {
    Map<String, int>? viewHistory,
    Map<String, int>? searchHistory,
    List<String>? favoriteItems,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final recommendations = <Recommendation>[];
    final random = Random();

    // توصيات بناءً على تفضيلات الفئات
    for (final category in categoryPreferences.keys) {
      final preference = categoryPreferences[category]!;
      final count = (preference * 5).round();
      
      for (int i = 0; i < count; i++) {
        recommendations.add(_generateRecommendation(
          category: category,
          profile: profile,
          isPersonalized: true,
          confidenceScore: preference,
        ));
      }
    }

    // توصيات رائجة
    for (int i = 0; i < 8; i++) {
      recommendations.add(_generateRecommendation(
        category: _getRandomCategory(),
        profile: profile,
        isTrending: true,
        confidenceScore: 0.6 + random.nextDouble() * 0.3,
      ));
    }

    // توصيات عامة
    for (int i = 0; i < 10; i++) {
      recommendations.add(_generateRecommendation(
        category: _getRandomCategory(),
        profile: profile,
        confidenceScore: 0.4 + random.nextDouble() * 0.4,
      ));
    }

    // ترتيب التوصيات حسب نقاط الثقة
    recommendations.sort((a, b) => b.confidenceScore.compareTo(a.confidenceScore));

    return recommendations.take(30).toList();
  }

  /// البحث عن منتجات مشابهة
  Future<List<Recommendation>> findSimilarProducts(
    String productId,
    List<Recommendation> allRecommendations,
  ) async {
    await Future.delayed(const Duration(milliseconds: 100));

    // محاكاة خوارزمية التشابه
    final similar = allRecommendations
        .where((rec) => rec.id != productId)
        .take(6)
        .toList();

    return similar;
  }

  /// إنشاء توصية واحدة
  Recommendation _generateRecommendation({
    required String category,
    required UserProfile profile,
    bool isPersonalized = false,
    bool isTrending = false,
    required double confidenceScore,
  }) {
    final random = Random();
    final products = _getProductsForCategory(category);
    final product = products[random.nextInt(products.length)];
    
    return Recommendation(
      id: 'rec_${random.nextInt(10000)}',
      title: product,
      description: 'وصف تفصيلي للمنتج $product',
      category: category,
      price: (random.nextInt(5000) + 100).toDouble(),
      imageUrl: 'https://via.placeholder.com/300x200',
      rating: 3.0 + random.nextDouble() * 2.0,
      isPersonalized: isPersonalized,
      isTrending: isTrending,
      confidenceScore: confidenceScore,
      reason: _getRecommendationReason(isPersonalized, isTrending, category),
      tags: _generateTags(category),
    );
  }

  /// الحصول على منتجات لفئة معينة
  List<String> _getProductsForCategory(String category) {
    final productMap = {
      'إلكترونيات': ['آيفون 15', 'سامسونج جالاكسي', 'لابتوب ديل', 'آيباد برو'],
      'مركبات': ['تويوتا كامري', 'هوندا أكورد', 'بي إم دبليو X5', 'مرسيدس C-Class'],
      'عقارات': ['شقة 3 غرف', 'فيلا للبيع', 'مكتب تجاري', 'أرض للاستثمار'],
      'أزياء': ['ساعة أبل', 'حقيبة لويس فيتون', 'حذاء نايك', 'نظارة ريبان'],
      'أثاث': ['كنبة جلد', 'طاولة طعام', 'سرير ملكي', 'خزانة ملابس'],
    };

    return productMap[category] ?? ['منتج عام'];
  }

  /// الحصول على فئة عشوائية
  String _getRandomCategory() {
    final categories = ['إلكترونيات', 'مركبات', 'عقارات', 'أزياء', 'أثاث'];
    return categories[Random().nextInt(categories.length)];
  }

  /// الحصول على سبب التوصية
  String _getRecommendationReason(bool isPersonalized, bool isTrending, String category) {
    if (isPersonalized) {
      return 'مقترح لك بناءً على اهتماماتك في $category';
    } else if (isTrending) {
      return 'رائج الآن في $category';
    } else {
      return 'منتج مميز في $category';
    }
  }

  /// إنشاء علامات
  List<String> _generateTags(String category) {
    final tagMap = {
      'إلكترونيات': ['جديد', 'ضمان', 'توصيل مجاني'],
      'مركبات': ['فحص شامل', 'سعر مميز', 'تمويل متاح'],
      'عقارات': ['موقع مميز', 'سعر تنافسي', 'جاهز للسكن'],
      'أزياء': ['ماركة أصلية', 'خصم خاص', 'أحدث موضة'],
      'أثاث': ['جودة عالية', 'تصميم عصري', 'توصيل وتركيب'],
    };

    return tagMap[category] ?? ['منتج مميز'];
  }
}

/// نموذج ملف المستخدم
class UserProfile {
  int age;
  String gender;
  String location;
  List<String> interests;
  PriceRange priceRange;

  UserProfile({
    this.age = 25,
    this.gender = 'unknown',
    this.location = 'غير محدد',
    this.interests = const [],
    PriceRange? priceRange,
  }) : priceRange = priceRange ?? PriceRange(min: 0, max: 10000);
}

/// نموذج التوصية
class Recommendation {
  final String id;
  final String title;
  final String description;
  final String category;
  final double price;
  final String imageUrl;
  final double rating;
  final bool isPersonalized;
  final bool isTrending;
  final double confidenceScore;
  final String reason;
  final List<String> tags;

  Recommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.price,
    required this.imageUrl,
    required this.rating,
    required this.isPersonalized,
    required this.isTrending,
    required this.confidenceScore,
    required this.reason,
    required this.tags,
  });
}

/// نطاق السعر
class PriceRange {
  double min;
  double max;

  PriceRange({required this.min, required this.max});
}
