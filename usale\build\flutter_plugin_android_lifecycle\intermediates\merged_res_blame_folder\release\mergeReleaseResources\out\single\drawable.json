[{"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/drawable/notification_bg_low.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/drawable/notification_bg.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/drawable/notification_icon_background.xml"}]