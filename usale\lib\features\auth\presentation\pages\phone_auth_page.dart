import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../../../core/theme/app_colors.dart';

import '../../../../services/firebase_auth_service.dart';
import '../widgets/auth_button.dart';
import '../../../home/<USER>/pages/main_page.dart';

class PhoneAuthPage extends StatefulWidget {
  const PhoneAuthPage({super.key});

  @override
  State<PhoneAuthPage> createState() => _PhoneAuthPageState();
}

class _PhoneAuthPageState extends State<PhoneAuthPage> {
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  String _countryCode = '+966';
  String? _verificationId;
  bool _isCodeSent = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  Future<void> _sendCode() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final phoneNumber = '$_countryCode${_phoneController.text.trim()}';
    final firebaseAuthService = Provider.of<FirebaseAuthService>(context, listen: false);
    
    final result = await firebaseAuthService.signInWithPhoneNumber(phoneNumber);
    
    setState(() => _isLoading = false);

    if (result.isSuccess && result.data != null) {
      setState(() {
        _verificationId = result.data['verificationId'];
        _isCodeSent = true;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _verifyCode() async {
    if (_codeController.text.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رمز التحقق المكون من 6 أرقام'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    final firebaseAuthService = Provider.of<FirebaseAuthService>(context, listen: false);
    
    final result = await firebaseAuthService.verifyPhoneCode(
      _verificationId!,
      _codeController.text.trim(),
    );
    
    setState(() => _isLoading = false);

    if (result.isSuccess) {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const MainPage(),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تسجيل الدخول بالهاتف'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 32),
                
                // Title
                Text(
                  _isCodeSent ? 'تأكيد رقم الهاتف' : 'تسجيل الدخول بالهاتف',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  _isCodeSent 
                    ? 'أدخل رمز التحقق المرسل إلى ${_countryCode}${_phoneController.text}'
                    : 'أدخل رقم هاتفك لتسجيل الدخول',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 48),
                
                if (!_isCodeSent) ...[
                  // Phone number input
                  Row(
                    children: [
                      // Country code picker
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: CountryCodePicker(
                          onChanged: (country) {
                            _countryCode = country.dialCode!;
                          },
                          initialSelection: 'SA',
                          favorite: const ['+966', '+1', '+44'],
                          showCountryOnly: false,
                          showOnlyCountryWhenClosed: false,
                          alignLeft: false,
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      // Phone number field
                      Expanded(
                        child: TextFormField(
                          controller: _phoneController,
                          keyboardType: TextInputType.phone,
                          textDirection: TextDirection.ltr,
                          decoration: InputDecoration(
                            labelText: 'رقم الهاتف',
                            hintText: '501234567',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            prefixIcon: const Icon(Icons.phone),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال رقم الهاتف';
                            }
                            if (value.length < 9) {
                              return 'رقم الهاتف قصير جداً';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Send code button
                  AuthButton(
                    text: 'إرسال رمز التحقق',
                    onPressed: _isLoading ? null : _sendCode,
                    isLoading: _isLoading,
                  ),
                ] else ...[
                  // PIN code input
                  PinCodeTextField(
                    appContext: context,
                    length: 6,
                    controller: _codeController,
                    keyboardType: TextInputType.number,
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(12),
                      fieldHeight: 60,
                      fieldWidth: 50,
                      activeFillColor: AppColors.surface,
                      inactiveFillColor: AppColors.surface,
                      selectedFillColor: AppColors.surface,
                      activeColor: AppColors.primary,
                      inactiveColor: AppColors.border,
                      selectedColor: AppColors.primary,
                    ),
                    enableActiveFill: true,
                    onCompleted: (value) {
                      _verifyCode();
                    },
                    onChanged: (value) {},
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Verify button
                  AuthButton(
                    text: 'تأكيد الرمز',
                    onPressed: _isLoading ? null : _verifyCode,
                    isLoading: _isLoading,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Resend code
                  TextButton(
                    onPressed: _isLoading ? null : () {
                      setState(() {
                        _isCodeSent = false;
                        _codeController.clear();
                      });
                    },
                    child: const Text('إرسال رمز جديد'),
                  ),
                ],
                
                const Spacer(),
                
                // Back to login
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('العودة لتسجيل الدخول'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
