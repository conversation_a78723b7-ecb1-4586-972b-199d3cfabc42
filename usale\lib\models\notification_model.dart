import 'package:flutter/material.dart';

class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String body;
  final NotificationType type;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic> data;
  final String? imageUrl;
  final String? actionUrl;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.data = const {},
    this.imageUrl,
    this.actionUrl,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'system'),
        orElse: () => NotificationType.system,
      ),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      isRead: json['is_read'] ?? false,
      data: json['data'] != null ? Map<String, dynamic>.from(json['data']) : {},
      imageUrl: json['image_url'],
      actionUrl: json['action_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'is_read': isRead,
      'data': data,
      'image_url': imageUrl,
      'action_url': actionUrl,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    NotificationType? type,
    DateTime? createdAt,
    bool? isRead,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, createdAt: $createdAt, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum NotificationType {
  message,
  favorite,
  sale,
  promotion,
  system,
  reminder,
  security,
  update,
  productInterest,
  review,
  priceAlert,
}

class NotificationSettings {
  final bool enabled;
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final Map<NotificationType, bool> typeSettings;
  final List<String> mutedUsers;
  final DateTime? quietHoursStart;
  final DateTime? quietHoursEnd;

  NotificationSettings({
    this.enabled = true,
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.typeSettings = const {},
    this.mutedUsers = const [],
    this.quietHoursStart,
    this.quietHoursEnd,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    final typeSettingsMap = <NotificationType, bool>{};
    if (json['type_settings'] != null) {
      final typeSettingsJson = Map<String, dynamic>.from(json['type_settings']);
      for (final entry in typeSettingsJson.entries) {
        final type = NotificationType.values.firstWhere(
          (e) => e.toString().split('.').last == entry.key,
          orElse: () => NotificationType.system,
        );
        typeSettingsMap[type] = entry.value as bool;
      }
    }

    return NotificationSettings(
      enabled: json['enabled'] ?? true,
      pushNotifications: json['push_notifications'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      smsNotifications: json['sms_notifications'] ?? false,
      typeSettings: typeSettingsMap,
      mutedUsers: List<String>.from(json['muted_users'] ?? []),
      quietHoursStart: json['quiet_hours_start'] != null 
          ? DateTime.parse(json['quiet_hours_start']) 
          : null,
      quietHoursEnd: json['quiet_hours_end'] != null 
          ? DateTime.parse(json['quiet_hours_end']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final typeSettingsJson = <String, bool>{};
    for (final entry in typeSettings.entries) {
      typeSettingsJson[entry.key.toString().split('.').last] = entry.value;
    }

    return {
      'enabled': enabled,
      'push_notifications': pushNotifications,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'type_settings': typeSettingsJson,
      'muted_users': mutedUsers,
      'quiet_hours_start': quietHoursStart?.toIso8601String(),
      'quiet_hours_end': quietHoursEnd?.toIso8601String(),
    };
  }

  NotificationSettings copyWith({
    bool? enabled,
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    Map<NotificationType, bool>? typeSettings,
    List<String>? mutedUsers,
    DateTime? quietHoursStart,
    DateTime? quietHoursEnd,
  }) {
    return NotificationSettings(
      enabled: enabled ?? this.enabled,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      typeSettings: typeSettings ?? this.typeSettings,
      mutedUsers: mutedUsers ?? this.mutedUsers,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
    );
  }

  bool isTypeEnabled(NotificationType type) {
    return enabled && (typeSettings[type] ?? true);
  }

  bool isUserMuted(String userId) {
    return mutedUsers.contains(userId);
  }

  bool isInQuietHours() {
    if (quietHoursStart == null || quietHoursEnd == null) return false;
    
    final now = DateTime.now();
    final currentTime = TimeOfDay.fromDateTime(now);
    final startTime = TimeOfDay.fromDateTime(quietHoursStart!);
    final endTime = TimeOfDay.fromDateTime(quietHoursEnd!);
    
    // Handle quiet hours that span midnight
    if (startTime.hour > endTime.hour || 
        (startTime.hour == endTime.hour && startTime.minute > endTime.minute)) {
      return currentTime.hour >= startTime.hour || currentTime.hour <= endTime.hour;
    } else {
      return currentTime.hour >= startTime.hour && currentTime.hour <= endTime.hour;
    }
  }

  @override
  String toString() {
    return 'NotificationSettings(enabled: $enabled, pushNotifications: $pushNotifications, emailNotifications: $emailNotifications)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationSettings &&
        other.enabled == enabled &&
        other.pushNotifications == pushNotifications &&
        other.emailNotifications == emailNotifications &&
        other.smsNotifications == smsNotifications;
  }

  @override
  int get hashCode {
    return Object.hash(
      enabled,
      pushNotifications,
      emailNotifications,
      smsNotifications,
    );
  }
}
