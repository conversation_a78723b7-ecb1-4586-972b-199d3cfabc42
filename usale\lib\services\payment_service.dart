import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/payment_model.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class PaymentService extends ChangeNotifier {
  // Singleton pattern
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();

  List<PaymentMethodModel> _paymentMethods = [];
  List<TransactionModel> _transactions = [];
  WalletModel? _wallet;
  bool _isLoading = false;

  // Getters
  List<PaymentMethodModel> get paymentMethods => _paymentMethods;
  List<TransactionModel> get transactions => _transactions;
  WalletModel? get wallet => _wallet;
  bool get isLoading => _isLoading;

  // Initialize payment service
  Future<void> initialize() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    await loadPaymentMethods();
    await loadWallet();
    await loadTransactions();
  }

  // Load payment methods
  Future<void> loadPaymentMethods() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // In real app, load from database
      _paymentMethods = await _getMockPaymentMethods(currentUser.id);
    } catch (e) {
      debugPrint('Error loading payment methods: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load wallet
  Future<void> loadWallet() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    try {
      // In real app, load from database
      _wallet = await _getMockWallet(currentUser.id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading wallet: $e');
    }
  }

  // Load transactions
  Future<void> loadTransactions() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    try {
      // In real app, load from database
      _transactions = await _getMockTransactions(currentUser.id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading transactions: $e');
    }
  }

  // Add payment method
  Future<PaymentResult> addPaymentMethod({
    required PaymentMethodType type,
    required String cardNumber,
    required String expiryDate,
    required String cvv,
    required String holderName,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return PaymentResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Validate card details
      if (!_validateCardNumber(cardNumber)) {
        return PaymentResult.error('رقم البطاقة غير صحيح');
      }

      if (!_validateExpiryDate(expiryDate)) {
        return PaymentResult.error('تاريخ انتهاء الصلاحية غير صحيح');
      }

      if (!_validateCVV(cvv)) {
        return PaymentResult.error('رمز الأمان غير صحيح');
      }

      final paymentMethod = PaymentMethodModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser.id,
        type: type,
        cardNumber: '**** **** **** ${cardNumber.substring(cardNumber.length - 4)}',
        expiryDate: expiryDate,
        holderName: holderName,
        isDefault: _paymentMethods.isEmpty,
        createdAt: DateTime.now(),
      );

      _paymentMethods.add(paymentMethod);
      notifyListeners();

      return PaymentResult.success('تم إضافة طريقة الدفع بنجاح');
    } catch (e) {
      debugPrint('Error adding payment method: $e');
      return PaymentResult.error('حدث خطأ أثناء إضافة طريقة الدفع');
    }
  }

  // Process payment
  Future<PaymentResult> processPayment({
    required String paymentMethodId,
    required double amount,
    required String currency,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return PaymentResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Find payment method
      final paymentMethod = _paymentMethods.firstWhere(
        (method) => method.id == paymentMethodId,
        orElse: () => PaymentMethodModel(
          id: '',
          userId: '',
          type: PaymentMethodType.creditCard,
          cardNumber: '',
          expiryDate: '',
          holderName: '',
          createdAt: DateTime.now(),
        ),
      );

      if (paymentMethod.id.isEmpty) {
        return PaymentResult.error('طريقة الدفع غير موجودة');
      }

      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // Create transaction
      final transaction = TransactionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser.id,
        type: TransactionType.payment,
        amount: amount,
        currency: currency,
        status: TransactionStatus.completed,
        description: description,
        paymentMethodId: paymentMethodId,
        metadata: metadata ?? {},
        createdAt: DateTime.now(),
      );

      _transactions.insert(0, transaction);
      notifyListeners();

      return PaymentResult.success('تم الدفع بنجاح', transaction);
    } catch (e) {
      debugPrint('Error processing payment: $e');
      return PaymentResult.error('فشل في معالجة الدفع');
    }
  }

  // Add money to wallet
  Future<PaymentResult> addMoneyToWallet({
    required double amount,
    required String paymentMethodId,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return PaymentResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      if (_wallet == null) {
        return PaymentResult.error('المحفظة غير متاحة');
      }

      // Process payment first
      final paymentResult = await processPayment(
        paymentMethodId: paymentMethodId,
        amount: amount,
        currency: 'SAR',
        description: 'إضافة رصيد للمحفظة',
      );

      if (!paymentResult.isSuccess) {
        return paymentResult;
      }

      // Update wallet balance
      _wallet = _wallet!.copyWith(
        balance: _wallet!.balance + amount,
        updatedAt: DateTime.now(),
      );

      // Create wallet transaction
      final transaction = TransactionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser.id,
        type: TransactionType.walletTopup,
        amount: amount,
        currency: 'SAR',
        status: TransactionStatus.completed,
        description: 'إضافة رصيد للمحفظة',
        createdAt: DateTime.now(),
      );

      _transactions.insert(0, transaction);
      notifyListeners();

      return PaymentResult.success('تم إضافة الرصيد بنجاح');
    } catch (e) {
      debugPrint('Error adding money to wallet: $e');
      return PaymentResult.error('حدث خطأ أثناء إضافة الرصيد');
    }
  }

  // Pay with wallet
  Future<PaymentResult> payWithWallet({
    required double amount,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return PaymentResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      if (_wallet == null || _wallet!.balance < amount) {
        return PaymentResult.error('رصيد المحفظة غير كافي');
      }

      // Deduct from wallet
      _wallet = _wallet!.copyWith(
        balance: _wallet!.balance - amount,
        updatedAt: DateTime.now(),
      );

      // Create transaction
      final transaction = TransactionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser.id,
        type: TransactionType.walletPayment,
        amount: -amount,
        currency: 'SAR',
        status: TransactionStatus.completed,
        description: description,
        metadata: metadata ?? {},
        createdAt: DateTime.now(),
      );

      _transactions.insert(0, transaction);
      notifyListeners();

      return PaymentResult.success('تم الدفع من المحفظة بنجاح', transaction);
    } catch (e) {
      debugPrint('Error paying with wallet: $e');
      return PaymentResult.error('حدث خطأ أثناء الدفع');
    }
  }

  // Validation methods
  bool _validateCardNumber(String cardNumber) {
    final cleanNumber = cardNumber.replaceAll(' ', '');
    return cleanNumber.length >= 13 && cleanNumber.length <= 19;
  }

  bool _validateExpiryDate(String expiryDate) {
    final parts = expiryDate.split('/');
    if (parts.length != 2) return false;
    
    final month = int.tryParse(parts[0]);
    final year = int.tryParse(parts[1]);
    
    if (month == null || year == null) return false;
    if (month < 1 || month > 12) return false;
    
    final now = DateTime.now();
    final expiry = DateTime(2000 + year, month);
    
    return expiry.isAfter(now);
  }

  bool _validateCVV(String cvv) {
    return cvv.length >= 3 && cvv.length <= 4;
  }

  // Mock data methods
  Future<List<PaymentMethodModel>> _getMockPaymentMethods(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      PaymentMethodModel(
        id: '1',
        userId: userId,
        type: PaymentMethodType.creditCard,
        cardNumber: '**** **** **** 1234',
        expiryDate: '12/25',
        holderName: 'أحمد محمد',
        isDefault: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    ];
  }

  Future<WalletModel> _getMockWallet(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return WalletModel(
      id: '1',
      userId: userId,
      balance: 1250.50,
      currency: 'SAR',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
    );
  }

  Future<List<TransactionModel>> _getMockTransactions(String userId) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [
      TransactionModel(
        id: '1',
        userId: userId,
        type: TransactionType.payment,
        amount: -299.99,
        currency: 'SAR',
        status: TransactionStatus.completed,
        description: 'شراء آيفون 15 برو ماكس',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      TransactionModel(
        id: '2',
        userId: userId,
        type: TransactionType.walletTopup,
        amount: 500.00,
        currency: 'SAR',
        status: TransactionStatus.completed,
        description: 'إضافة رصيد للمحفظة',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }
}

class PaymentResult {
  final bool isSuccess;
  final String message;
  final TransactionModel? transaction;

  PaymentResult._(this.isSuccess, this.message, [this.transaction]);

  factory PaymentResult.success(String message, [TransactionModel? transaction]) {
    return PaymentResult._(true, message, transaction);
  }

  factory PaymentResult.error(String message) {
    return PaymentResult._(false, message);
  }
}
