class ProductModel {
  final String id;
  final String title;
  final String description;
  final double price;
  final String currency;
  final String category;
  final String condition;
  final List<String> images;
  final String location;
  final String sellerId;
  final String sellerName;
  final String? sellerImage;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;
  final bool isFeatured;
  final int viewsCount;
  final int favoritesCount;
  final double? latitude;
  final double averageRating;
  final int reviewsCount;
  final double? longitude;
  final Map<String, dynamic> specifications;
  final List<String> tags;
  final ProductStatus status;

  ProductModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    this.currency = 'SAR',
    required this.category,
    required this.condition,
    this.images = const [],
    required this.location,
    required this.sellerId,
    required this.sellerName,
    this.sellerImage,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isFeatured = false,
    this.viewsCount = 0,
    this.favoritesCount = 0,
    this.latitude,
    this.longitude,
    this.specifications = const {},
    this.tags = const [],
    this.status = ProductStatus.available,
    this.averageRating = 0.0,
    this.reviewsCount = 0,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'SAR',
      category: json['category'] ?? '',
      condition: json['condition'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      location: json['location'] ?? '',
      sellerId: json['seller_id'] ?? '',
      sellerName: json['seller_name'] ?? '',
      sellerImage: json['seller_image'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      isActive: json['is_active'] ?? true,
      isFeatured: json['is_featured'] ?? false,
      viewsCount: json['views_count'] ?? 0,
      favoritesCount: json['favorites_count'] ?? 0,
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      specifications: Map<String, dynamic>.from(json['specifications'] ?? {}),
      tags: List<String>.from(json['tags'] ?? []),
      status: ProductStatus.values.firstWhere(
        (e) => e.toString().split('.').last == (json['status'] ?? 'available'),
        orElse: () => ProductStatus.available,
      ),
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      reviewsCount: json['reviews_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'currency': currency,
      'category': category,
      'condition': condition,
      'images': images,
      'location': location,
      'seller_id': sellerId,
      'seller_name': sellerName,
      'seller_image': sellerImage,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_active': isActive,
      'is_featured': isFeatured,
      'views_count': viewsCount,
      'favorites_count': favoritesCount,
      'latitude': latitude,
      'longitude': longitude,
      'specifications': specifications,
      'tags': tags,
      'status': status.toString().split('.').last,
      'average_rating': averageRating,
      'reviews_count': reviewsCount,
    };
  }

  ProductModel copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? currency,
    String? category,
    String? condition,
    List<String>? images,
    String? location,
    String? sellerId,
    String? sellerName,
    String? sellerImage,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isFeatured,
    int? viewsCount,
    int? favoritesCount,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    ProductStatus? status,
    double? averageRating,
    int? reviewsCount,
  }) {
    return ProductModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      category: category ?? this.category,
      condition: condition ?? this.condition,
      images: images ?? this.images,
      location: location ?? this.location,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      sellerImage: sellerImage ?? this.sellerImage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      viewsCount: viewsCount ?? this.viewsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      specifications: specifications ?? this.specifications,
      tags: tags ?? this.tags,
      status: status ?? this.status,
      averageRating: averageRating ?? this.averageRating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
    );
  }

  String get formattedPrice {
    if (price == 0) return 'مجاناً';
    return '${price.toStringAsFixed(0)} $currency';
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  String get mainImage {
    return images.isNotEmpty ? images.first : '';
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, title: $title, price: $price, category: $category, sellerId: $sellerId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum ProductStatus {
  available,
  sold,
  reserved,
  expired,
  deleted,
}
