import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import 'database_service.dart';

enum AuthState { authenticated, unauthenticated, loading }

class AuthService extends ChangeNotifier {
  static const String _userKey = 'current_user';
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  
  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  UserModel? _currentUser;
  String? _authToken;
  String? _refreshToken;
  AuthState _authState = AuthState.loading;
  Timer? _tokenRefreshTimer;
  final DatabaseService _databaseService = DatabaseService();

  // Getters
  UserModel? get currentUser => _currentUser;
  String? get authToken => _authToken;
  bool get isAuthenticated => _currentUser != null && _authToken != null;
  bool get isLoading => _authState == AuthState.loading;
  AuthState get authState => _authState;

  // Initialize auth service
  Future<void> initialize() async {
    _authState = AuthState.loading;
    notifyListeners();
    
    try {
      await _loadStoredAuth();
      if (_currentUser != null && _authToken != null) {
        _authState = AuthState.authenticated;
        _startTokenRefreshTimer();
      } else {
        _authState = AuthState.unauthenticated;
      }
    } catch (e) {
      _authState = AuthState.unauthenticated;
      debugPrint('Auth initialization error: $e');
    }
    
    notifyListeners();
  }

  // Load stored authentication data
  Future<void> _loadStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    
    final userJson = prefs.getString(_userKey);
    final token = prefs.getString(_tokenKey);
    final refreshToken = prefs.getString(_refreshTokenKey);
    
    if (userJson != null && token != null) {
      _currentUser = UserModel.fromJson(jsonDecode(userJson));
      _authToken = token;
      _refreshToken = refreshToken;
    }
  }

  // Save authentication data
  Future<void> _saveAuth() async {
    final prefs = await SharedPreferences.getInstance();
    
    if (_currentUser != null && _authToken != null) {
      await prefs.setString(_userKey, jsonEncode(_currentUser!.toJson()));
      await prefs.setString(_tokenKey, _authToken!);
      if (_refreshToken != null) {
        await prefs.setString(_refreshTokenKey, _refreshToken!);
      }
    }
  }

  // Clear stored authentication data
  Future<void> _clearAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
  }

  // Login with email and password
  Future<AuthResult> login(String email, String password) async {
    try {
      _authState = AuthState.loading;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Check user in database
      if (email.isNotEmpty && password.length >= 6) {
        // Try to find user in database
        UserModel? existingUser = await _databaseService.getUserByEmail(email);

        if (existingUser != null) {
          // User exists, simulate password check
          _currentUser = existingUser.copyWith(
            updatedAt: DateTime.now(),
          );

          // Update user in database
          await _databaseService.updateUser(_currentUser!);
        } else {
          // Create new user
          _currentUser = UserModel(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            name: email.split('@').first,
            email: email,
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isVerified: true,
            userType: UserType.buyer,
            status: AccountStatus.active,
            settings: UserSettings(),
          );

          // Save user to database
          await _databaseService.insertUser(_currentUser!);
        }

        _authToken = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _refreshToken = 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

        await _saveAuth();
        _authState = AuthState.authenticated;
        _startTokenRefreshTimer();
        notifyListeners();

        return AuthResult.success('تم تسجيل الدخول بنجاح');
      } else {
        _authState = AuthState.unauthenticated;
        notifyListeners();
        return AuthResult.error('بيانات الدخول غير صحيحة');
      }
    } catch (e) {
      _authState = AuthState.unauthenticated;
      notifyListeners();
      return AuthResult.error('حدث خطأ أثناء تسجيل الدخول');
    }
  }

  // Register new user
  Future<AuthResult> register({
    required String name,
    required String email,
    required String password,
    String? phone,
    UserType userType = UserType.buyer,
  }) async {
    try {
      _authState = AuthState.loading;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Check if user already exists
      if (name.isNotEmpty && email.isNotEmpty && password.length >= 6) {
        // Check if email already exists
        UserModel? existingUser = await _databaseService.getUserByEmail(email);

        if (existingUser != null) {
          _authState = AuthState.unauthenticated;
          notifyListeners();
          return AuthResult.error('البريد الإلكتروني مستخدم بالفعل');
        }

        // Create new user
        _currentUser = UserModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          email: email,
          phone: phone,
          createdAt: DateTime.now(),
          userType: userType,
          status: AccountStatus.active,
          settings: UserSettings(),
        );

        // Save user to database
        await _databaseService.insertUser(_currentUser!);

        _authToken = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _refreshToken = 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

        await _saveAuth();
        _authState = AuthState.authenticated;
        _startTokenRefreshTimer();
        notifyListeners();

        return AuthResult.success('تم إنشاء الحساب بنجاح');
      } else {
        _authState = AuthState.unauthenticated;
        notifyListeners();
        return AuthResult.error('يرجى ملء جميع البيانات المطلوبة');
      }
    } catch (e) {
      _authState = AuthState.unauthenticated;
      notifyListeners();
      return AuthResult.error('حدث خطأ أثناء إنشاء الحساب');
    }
  }

  // Logout
  Future<void> logout() async {
    _tokenRefreshTimer?.cancel();
    _currentUser = null;
    _authToken = null;
    _refreshToken = null;
    _authState = AuthState.unauthenticated;
    
    await _clearAuth();
    notifyListeners();
  }

  // Update user profile
  Future<AuthResult> updateProfile(UserModel updatedUser) async {
    try {
      if (_currentUser == null) {
        return AuthResult.error('المستخدم غير مسجل الدخول');
      }

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _currentUser = updatedUser.copyWith(
        updatedAt: DateTime.now(),
      );
      
      await _saveAuth();
      notifyListeners();
      
      return AuthResult.success('تم تحديث الملف الشخصي بنجاح');
    } catch (e) {
      return AuthResult.error('حدث خطأ أثناء تحديث الملف الشخصي');
    }
  }

  // Change password
  Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    try {
      if (_currentUser == null) {
        return AuthResult.error('المستخدم غير مسجل الدخول');
      }

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // In real app, verify current password and update
      if (newPassword.length >= 6) {
        return AuthResult.success('تم تغيير كلمة المرور بنجاح');
      } else {
        return AuthResult.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }
    } catch (e) {
      return AuthResult.error('حدث خطأ أثناء تغيير كلمة المرور');
    }
  }

  // Verify email
  Future<AuthResult> verifyEmail(String verificationCode) async {
    try {
      if (_currentUser == null) {
        return AuthResult.error('المستخدم غير مسجل الدخول');
      }

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _currentUser = _currentUser!.copyWith(
        isVerified: true,
        updatedAt: DateTime.now(),
      );
      
      await _saveAuth();
      notifyListeners();
      
      return AuthResult.success('تم تأكيد البريد الإلكتروني بنجاح');
    } catch (e) {
      return AuthResult.error('حدث خطأ أثناء تأكيد البريد الإلكتروني');
    }
  }

  // Start token refresh timer
  void _startTokenRefreshTimer() {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = Timer.periodic(
      const Duration(minutes: 30),
      (timer) => _refreshAuthToken(),
    );
  }

  // Refresh authentication token
  Future<void> _refreshAuthToken() async {
    try {
      if (_refreshToken == null) return;
      
      // Simulate token refresh API call
      await Future.delayed(const Duration(seconds: 1));
      
      _authToken = 'refreshed_token_${DateTime.now().millisecondsSinceEpoch}';
      await _saveAuth();
    } catch (e) {
      debugPrint('Token refresh error: $e');
      // If refresh fails, logout user
      await logout();
    }
  }

  @override
  void dispose() {
    _tokenRefreshTimer?.cancel();
    super.dispose();
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;
  final dynamic data;

  AuthResult._(this.isSuccess, this.message, [this.data]);

  factory AuthResult.success(String message, [dynamic data]) {
    return AuthResult._(true, message, data);
  }

  factory AuthResult.error(String message) {
    return AuthResult._(false, message);
  }
}
