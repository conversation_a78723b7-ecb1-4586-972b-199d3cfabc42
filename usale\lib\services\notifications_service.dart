import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
// import '../services/database_service.dart'; // Removed unused import
import '../services/auth_service.dart';

class NotificationsService extends ChangeNotifier {
  // Singleton pattern
  static final NotificationsService _instance = NotificationsService._internal();
  factory NotificationsService() => _instance;
  NotificationsService._internal();

  // final DatabaseService _databaseService = DatabaseService(); // Removed unused field
  final AuthService _authService = AuthService();

  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  Timer? _refreshTimer;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  // Initialize service
  Future<void> initialize() async {
    await loadNotifications();
    _startPeriodicRefresh();
  }

  // Load notifications
  Future<void> loadNotifications() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // In real app, load from database
      _notifications = await _getMockNotifications(currentUser.id);
    } catch (e) {
      debugPrint('Error loading notifications: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add new notification
  Future<void> addNotification({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title,
        body: body,
        type: type,
        data: data ?? {},
        createdAt: DateTime.now(),
      );

      _notifications.insert(0, notification);
      notifyListeners();

      // In real app, save to database and send push notification
    } catch (e) {
      debugPrint('Error adding notification: $e');
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        notifyListeners();
        
        // In real app, update in database
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      bool hasChanges = false;
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(isRead: true);
          hasChanges = true;
        }
      }
      
      if (hasChanges) {
        notifyListeners();
        // In real app, update in database
      }
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      notifyListeners();
      
      // In real app, delete from database
    } catch (e) {
      debugPrint('Error deleting notification: $e');
    }
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      notifyListeners();
      
      // In real app, delete from database
    } catch (e) {
      debugPrint('Error clearing notifications: $e');
    }
  }

  // Get notifications by type
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get unread notifications
  List<NotificationModel> getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // Send notification to user
  Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) async {
    await addNotification(
      userId: userId,
      title: title,
      body: body,
      type: type,
      data: data,
    );
  }

  // Send notification for new message
  Future<void> sendMessageNotification({
    required String userId,
    required String senderName,
    required String message,
    required String chatId,
  }) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'رسالة جديدة من $senderName',
      body: message,
      type: NotificationType.message,
      data: {'chat_id': chatId, 'sender_name': senderName},
    );
  }

  // Send notification for product interest
  Future<void> sendProductInterestNotification({
    required String sellerId,
    required String buyerName,
    required String productTitle,
    required String productId,
  }) async {
    await sendNotificationToUser(
      userId: sellerId,
      title: 'اهتمام بمنتجك',
      body: '$buyerName مهتم بـ $productTitle',
      type: NotificationType.productInterest,
      data: {'product_id': productId, 'buyer_name': buyerName},
    );
  }

  // Send notification for new review
  Future<void> sendReviewNotification({
    required String userId,
    required String reviewerName,
    required double rating,
    required String targetType,
    required String targetId,
  }) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'تقييم جديد',
      body: '$reviewerName قام بتقييمك بـ $rating نجوم',
      type: NotificationType.review,
      data: {
        'target_id': targetId,
        'target_type': targetType,
        'rating': rating,
        'reviewer_name': reviewerName,
      },
    );
  }

  // Send notification for price drop
  Future<void> sendPriceDropNotification({
    required String userId,
    required String productTitle,
    required double oldPrice,
    required double newPrice,
    required String productId,
  }) async {
    final discount = ((oldPrice - newPrice) / oldPrice * 100).round();
    await sendNotificationToUser(
      userId: userId,
      title: 'انخفاض في السعر',
      body: '$productTitle انخفض بنسبة $discount%',
      type: NotificationType.priceAlert,
      data: {
        'product_id': productId,
        'old_price': oldPrice,
        'new_price': newPrice,
        'discount': discount,
      },
    );
  }

  // Start periodic refresh
  void _startPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      loadNotifications();
    });
  }

  // Mock data
  Future<List<NotificationModel>> _getMockNotifications(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return [
      NotificationModel(
        id: '1',
        userId: userId,
        title: 'رسالة جديدة',
        body: 'أحمد محمد أرسل لك رسالة',
        type: NotificationType.message,
        data: {'chat_id': '1', 'sender_name': 'أحمد محمد'},
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        isRead: false,
      ),
      NotificationModel(
        id: '2',
        userId: userId,
        title: 'اهتمام بمنتجك',
        body: 'فاطمة علي مهتمة بآيفون 15 برو ماكس',
        type: NotificationType.productInterest,
        data: {'product_id': '1', 'buyer_name': 'فاطمة علي'},
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: false,
      ),
      NotificationModel(
        id: '3',
        userId: userId,
        title: 'تقييم جديد',
        body: 'محمد السعيد قام بتقييمك بـ 5 نجوم',
        type: NotificationType.review,
        data: {
          'target_id': userId,
          'target_type': 'user',
          'rating': 5.0,
          'reviewer_name': 'محمد السعيد',
        },
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        isRead: true,
      ),
      NotificationModel(
        id: '4',
        userId: userId,
        title: 'انخفاض في السعر',
        body: 'لابتوب ديل XPS انخفض بنسبة 15%',
        type: NotificationType.priceAlert,
        data: {
          'product_id': '3',
          'old_price': 3200.0,
          'new_price': 2720.0,
          'discount': 15,
        },
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
      ),
    ];
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
}
