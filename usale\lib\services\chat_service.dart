import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import '../models/chat_conversation_model.dart';
import '../models/message_model.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class ChatService extends ChangeNotifier {
  // Singleton pattern
  static final ChatService _instance = ChatService._internal();
  factory ChatService() => _instance;
  ChatService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  final ImagePicker _imagePicker = ImagePicker();

  List<ChatModel> _chats = [];
  List<MessageModel> _currentMessages = [];
  String? _currentChatId;
  bool _isLoading = false;
  bool _isSendingMessage = false;

  // Stream controllers for real-time updates
  final StreamController<List<ChatModel>> _chatsController = StreamController<List<ChatModel>>.broadcast();
  final StreamController<List<MessageModel>> _messagesController = StreamController<List<MessageModel>>.broadcast();

  // Getters
  List<ChatModel> get chats => _chats;
  List<MessageModel> get currentMessages => _currentMessages;
  String? get currentChatId => _currentChatId;
  bool get isLoading => _isLoading;
  bool get isSendingMessage => _isSendingMessage;

  // Streams
  Stream<List<ChatModel>> get chatsStream => _chatsController.stream;
  Stream<List<MessageModel>> get messagesStream => _messagesController.stream;

  // Load user's chats
  Future<void> loadChats() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // In real app, load from database
      _chats = await _getMockChats(currentUser.id);
      _chatsController.add(_chats);
    } catch (e) {
      debugPrint('Error loading chats: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get or create chat with another user
  Future<ChatModel?> getOrCreateChat({
    required String otherUserId,
    required String otherUserName,
    String? productId,
    String? productTitle,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return null;

    try {
      // Check if chat already exists
      final existingChat = _chats.firstWhere(
        (chat) => (chat.participant1Id == currentUser.id && chat.participant2Id == otherUserId) ||
                  (chat.participant1Id == otherUserId && chat.participant2Id == currentUser.id),
        orElse: () => ChatModel(
          id: '',
          participant1Id: '',
          participant2Id: '',
          participant1Name: '',
          participant2Name: '',
          createdAt: DateTime.now(),
        ),
      );

      if (existingChat.id.isNotEmpty) {
        return existingChat;
      }

      // Create new chat
      final newChat = ChatModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        participant1Id: currentUser.id,
        participant2Id: otherUserId,
        participant1Name: currentUser.displayName,
        participant2Name: otherUserName,
        productId: productId,
        productTitle: productTitle,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // In real app, save to database
      _chats.insert(0, newChat);
      _chatsController.add(_chats);
      notifyListeners();

      return newChat;
    } catch (e) {
      debugPrint('Error creating chat: $e');
      return null;
    }
  }

  // Load messages for a specific chat
  Future<void> loadMessages(String chatId) async {
    _currentChatId = chatId;
    _isLoading = true;
    notifyListeners();

    try {
      // In real app, load from database
      _currentMessages = await _getMockMessages(chatId);
      _messagesController.add(_currentMessages);
      
      // Mark messages as read
      await _markMessagesAsRead(chatId);
    } catch (e) {
      debugPrint('Error loading messages: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Send text message
  Future<bool> sendMessage({
    required String chatId,
    required String content,
    MessageType type = MessageType.text,
    String? attachmentUrl,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    _isSendingMessage = true;
    notifyListeners();

    try {
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: currentUser.id,
        senderName: currentUser.displayName,
        content: content,
        type: type,
        attachmentUrl: attachmentUrl,
        createdAt: DateTime.now(),
        isRead: false,
      );

      // Add to current messages
      _currentMessages.add(message);
      _messagesController.add(_currentMessages);

      // Update chat's last message
      final chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
      if (chatIndex != -1) {
        _chats[chatIndex] = _chats[chatIndex].copyWith(
          lastMessage: content,
          lastMessageAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        _chatsController.add(_chats);
      }

      // In real app, save to database and send via socket
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error sending message: $e');
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  // Send image message
  Future<bool> sendImageMessage(String chatId) async {
    try {
      final image = await _imagePicker.pickImage(source: ImageSource.gallery);
      if (image == null) return false;

      // In real app, upload image to server
      final imageUrl = image.path; // Mock URL

      return await sendMessage(
        chatId: chatId,
        content: 'صورة',
        type: MessageType.image,
        attachmentUrl: imageUrl,
      );
    } catch (e) {
      debugPrint('Error sending image: $e');
      return false;
    }
  }

  // Mark messages as read
  Future<void> _markMessagesAsRead(String chatId) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    try {
      // Mark unread messages as read
      for (int i = 0; i < _currentMessages.length; i++) {
        if (_currentMessages[i].senderId != currentUser.id && !_currentMessages[i].isRead) {
          _currentMessages[i] = _currentMessages[i].copyWith(isRead: true);
        }
      }

      // Update chat's unread count
      final chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
      if (chatIndex != -1) {
        _chats[chatIndex] = _chats[chatIndex].copyWith(unreadCount: 0);
        _chatsController.add(_chats);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error marking messages as read: $e');
    }
  }

  // Get unread messages count
  int getUnreadCount() {
    return _chats.fold(0, (sum, chat) => sum + chat.unreadCount);
  }

  // Delete chat
  Future<bool> deleteChat(String chatId) async {
    try {
      _chats.removeWhere((chat) => chat.id == chatId);
      _chatsController.add(_chats);
      
      if (_currentChatId == chatId) {
        _currentChatId = null;
        _currentMessages.clear();
        _messagesController.add(_currentMessages);
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting chat: $e');
      return false;
    }
  }

  // Clear current chat
  void clearCurrentChat() {
    _currentChatId = null;
    _currentMessages.clear();
    notifyListeners();
  }

  // Mock data methods
  Future<List<ChatModel>> _getMockChats(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      ChatModel(
        id: '1',
        participant1Id: userId,
        participant2Id: 'user2',
        participant1Name: 'أنت',
        participant2Name: 'أحمد محمد',
        participant2Image: '',
        productId: '1',
        productTitle: 'آيفون 15 برو ماكس',
        lastMessage: 'هل المنتج متوفر؟',
        lastMessageAt: DateTime.now().subtract(const Duration(minutes: 5)),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        unreadCount: 2,
      ),
      ChatModel(
        id: '2',
        participant1Id: userId,
        participant2Id: 'user3',
        participant1Name: 'أنت',
        participant2Name: 'سارة أحمد',
        participant2Image: '',
        productId: '2',
        productTitle: 'سيارة تويوتا كامري',
        lastMessage: 'شكراً لك',
        lastMessageAt: DateTime.now().subtract(const Duration(hours: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        unreadCount: 0,
      ),
    ];
  }

  Future<List<MessageModel>> _getMockMessages(String chatId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    final currentUser = _authService.currentUser;
    if (currentUser == null) return [];

    return [
      MessageModel(
        id: '1',
        chatId: chatId,
        senderId: 'user2',
        senderName: 'أحمد محمد',
        content: 'السلام عليكم، هل المنتج متوفر؟',
        type: MessageType.text,
        createdAt: DateTime.now().subtract(const Duration(minutes: 10)),
        isRead: true,
      ),
      MessageModel(
        id: '2',
        chatId: chatId,
        senderId: currentUser.id,
        senderName: currentUser.displayName,
        content: 'وعليكم السلام، نعم متوفر',
        type: MessageType.text,
        createdAt: DateTime.now().subtract(const Duration(minutes: 8)),
        isRead: true,
      ),
      MessageModel(
        id: '3',
        chatId: chatId,
        senderId: 'user2',
        senderName: 'أحمد محمد',
        content: 'ممكن أشوف صور أكثر؟',
        type: MessageType.text,
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        isRead: false,
      ),
    ];
  }

  @override
  void dispose() {
    _chatsController.close();
    _messagesController.close();
    super.dispose();
  }
}
