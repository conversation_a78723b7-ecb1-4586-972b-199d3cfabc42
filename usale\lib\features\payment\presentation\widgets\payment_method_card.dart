import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/payment_method_model.dart';

class PaymentMethodCard extends StatelessWidget {
  final PaymentMethodModel paymentMethod;
  final bool isSelected;
  final VoidCallback onSelected;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const PaymentMethodCard({
    super.key,
    required this.paymentMethod,
    required this.isSelected,
    required this.onSelected,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onSelected,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Payment Method Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getIconBackgroundColor(),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getPaymentMethodIcon(),
                  color: _getIconColor(),
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // Payment Method Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      paymentMethod.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getSubtitle(),
                      style: const TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                    if (paymentMethod.isDefault) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'افتراضي',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Selection Indicator
              if (isSelected)
                Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                )
              else
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.border, width: 2),
                    shape: BoxShape.circle,
                  ),
                ),

              // Actions Menu (for saved payment methods)
              if (paymentMethod.id != 'cod' && (onEdit != null || onDelete != null))
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEdit?.call();
                        break;
                      case 'delete':
                        onDelete?.call();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (onEdit != null)
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                    if (onDelete != null)
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: AppColors.error),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: AppColors.error)),
                          ],
                        ),
                      ),
                  ],
                  child: const Icon(
                    Icons.more_vert,
                    color: AppColors.textSecondary,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getPaymentMethodIcon() {
    switch (paymentMethod.type) {
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        return Icons.credit_card;
      case PaymentMethodType.applePay:
        return Icons.apple;
      case PaymentMethodType.googlePay:
        return Icons.account_balance_wallet; // Google Pay icon alternative
      case PaymentMethodType.stcPay:
        return Icons.phone_android;
      case PaymentMethodType.mada:
        return Icons.credit_card;
      case PaymentMethodType.cashOnDelivery:
        return Icons.money;
      case PaymentMethodType.bankTransfer:
        return Icons.account_balance;
      case PaymentMethodType.wallet:
        return Icons.account_balance_wallet;
    }
  }

  Color _getIconColor() {
    switch (paymentMethod.type) {
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        return AppColors.primary;
      case PaymentMethodType.applePay:
        return Colors.black;
      case PaymentMethodType.googlePay:
        return Colors.blue;
      case PaymentMethodType.stcPay:
        return Colors.purple;
      case PaymentMethodType.mada:
        return Colors.green;
      case PaymentMethodType.cashOnDelivery:
        return Colors.orange;
      case PaymentMethodType.bankTransfer:
        return AppColors.primary;
      case PaymentMethodType.wallet:
        return Colors.indigo;
    }
  }

  Color _getIconBackgroundColor() {
    return _getIconColor().withValues(alpha: 0.1);
  }

  String _getSubtitle() {
    switch (paymentMethod.type) {
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
      case PaymentMethodType.mada:
        if (paymentMethod.cardNumber != null) {
          return paymentMethod.maskedCardNumber;
        }
        return paymentMethod.typeDisplayName;
      case PaymentMethodType.applePay:
        return 'دفع سريع وآمن باستخدام Touch ID أو Face ID';
      case PaymentMethodType.googlePay:
        return 'دفع سريع وآمن باستخدام Google Pay';
      case PaymentMethodType.stcPay:
        return 'دفع عبر تطبيق STC Pay';
      case PaymentMethodType.cashOnDelivery:
        return 'ادفع نقداً عند استلام الطلب';
      case PaymentMethodType.bankTransfer:
        if (paymentMethod.bankName != null) {
          return 'تحويل من ${paymentMethod.bankName}';
        }
        return 'تحويل بنكي مباشر';
      case PaymentMethodType.wallet:
        if (paymentMethod.walletId != null) {
          return 'محفظة: ${paymentMethod.walletId}';
        }
        return 'محفظة رقمية';
    }
  }
}
