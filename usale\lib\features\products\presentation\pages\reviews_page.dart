import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/review_model.dart';
import '../../../../models/product_model.dart';
import '../../../../services/reviews_service.dart';
import '../../../../services/auth_service.dart';
import '../widgets/review_card.dart';
import '../widgets/review_summary_widget.dart';
import '../widgets/add_review_dialog.dart';

class ReviewsPage extends StatefulWidget {
  final ProductModel product;

  const ReviewsPage({
    super.key,
    required this.product,
  });

  @override
  State<ReviewsPage> createState() => _ReviewsPageState();
}

class _ReviewsPageState extends State<ReviewsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'الكل';
  String _selectedSort = 'الأحدث';

  final List<String> _filters = [
    'الكل',
    '5 نجوم',
    '4 نجوم',
    '3 نجوم',
    '2 نجوم',
    '1 نجمة',
    'مع صور',
    'مع تعليقات',
  ];

  final List<String> _sortOptions = [
    'الأحدث',
    'الأقدم',
    'الأعلى تقييماً',
    'الأقل تقييماً',
    'الأكثر فائدة',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadReviews();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadReviews() {
    final reviewsService = Provider.of<ReviewsService>(context, listen: false);
    reviewsService.loadProductReviews(widget.product.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('التقييمات والمراجعات'),
        backgroundColor: AppColors.surface,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'التقييمات'),
            Tab(text: 'إحصائيات'),
          ],
          indicatorColor: AppColors.primary,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showSortDialog,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildReviewsTab(),
          _buildStatisticsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddReviewDialog,
        icon: const Icon(Icons.rate_review),
        label: const Text('إضافة تقييم'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  Widget _buildReviewsTab() {
    return Consumer<ReviewsService>(
      builder: (context, reviewsService, child) {
        final reviews = reviewsService.getFilteredReviews(
          productId: widget.product.id,
          filter: _selectedFilter,
          sortBy: _selectedSort,
        );

        if (reviewsService.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (reviews.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // Review Summary
            Container(
              margin: const EdgeInsets.all(AppConstants.defaultPadding),
              child: ReviewSummaryWidget(
                productId: widget.product.id,
                averageRating: widget.product.averageRating,
                totalReviews: widget.product.reviewsCount,
              ),
            ),

            // Filter Chips
            Container(
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _filters.length,
                itemBuilder: (context, index) {
                  final filter = _filters[index];
                  final isSelected = filter == _selectedFilter;
                  
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(filter),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedFilter = filter;
                        });
                      },
                      backgroundColor: AppColors.surface,
                      selectedColor: AppColors.primary.withValues(alpha: 0.2),
                      checkmarkColor: AppColors.primary,
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Reviews List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                itemCount: reviews.length,
                itemBuilder: (context, index) {
                  final review = reviews[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: ReviewCard(
                      review: review,
                      onHelpful: () => _markReviewHelpful(review),
                      onReport: () => _reportReview(review),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatisticsTab() {
    return Consumer<ReviewsService>(
      builder: (context, reviewsService, child) {
        final summary = reviewsService.getReviewSummary(widget.product.id);
        
        if (summary == null) {
          return const Center(
            child: Text('لا توجد إحصائيات متاحة'),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Overall Rating Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Text(
                            summary.averageRating.toStringAsFixed(1),
                            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: List.generate(5, (index) {
                                  return Icon(
                                    index < summary.averageRating.round()
                                        ? Icons.star
                                        : Icons.star_border,
                                    color: AppColors.warning,
                                    size: 20,
                                  );
                                }),
                              ),
                              Text(
                                '${summary.totalReviews} تقييم',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      // Rating Distribution
                      ...List.generate(5, (index) {
                        final stars = 5 - index;
                        final count = summary.ratingDistribution[stars] ?? 0;
                        final percentage = summary.totalReviews > 0 
                            ? (count / summary.totalReviews) * 100 
                            : 0.0;
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Text('$stars'),
                              const SizedBox(width: 4),
                              const Icon(Icons.star, size: 16, color: AppColors.warning),
                              const SizedBox(width: 8),
                              Expanded(
                                child: LinearProgressIndicator(
                                  value: percentage / 100,
                                  backgroundColor: AppColors.border,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${percentage.toStringAsFixed(0)}%',
                                style: const TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Additional Statistics
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إحصائيات إضافية',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildStatItem('التقييمات مع صور', '${summary.reviewsWithImages}'),
                      _buildStatItem('التقييمات مع تعليقات', '${summary.reviewsWithComments}'),
                      _buildStatItem('المراجعات المفيدة', '${summary.helpfulReviews}'),
                      _buildStatItem('متوسط طول التعليق', '${summary.averageCommentLength} حرف'),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Recent Activity
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'النشاط الأخير',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildStatItem('تقييمات هذا الأسبوع', '${summary.weeklyReviews}'),
                      _buildStatItem('تقييمات هذا الشهر', '${summary.monthlyReviews}'),
                      _buildStatItem('آخر تقييم', summary.lastReviewDate),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: AppColors.textSecondary),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 80,
            color: AppColors.textLight,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد تقييمات بعد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من يقيم هذا المنتج',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textLight,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddReviewDialog,
            icon: const Icon(Icons.rate_review),
            label: const Text('إضافة تقييم'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية التقييمات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _filters.map((filter) {
            return RadioListTile<String>(
              title: Text(filter),
              value: filter,
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترتيب التقييمات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _sortOptions.map((option) {
            return RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: _selectedSort,
              onChanged: (value) {
                setState(() {
                  _selectedSort = value!;
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showAddReviewDialog() {
    final authService = Provider.of<AuthService>(context, listen: false);
    
    if (!authService.isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول لإضافة تقييم'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AddReviewDialog(
        product: widget.product,
        onReviewAdded: () {
          _loadReviews();
        },
      ),
    );
  }

  void _markReviewHelpful(ReviewModel review) {
    final reviewsService = Provider.of<ReviewsService>(context, listen: false);
    reviewsService.markReviewHelpful(review.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تسجيل رأيك في هذا التقييم'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _reportReview(ReviewModel review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن التقييم'),
        content: const Text('هل تريد الإبلاغ عن هذا التقييم كمحتوى غير مناسب؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final reviewsService = Provider.of<ReviewsService>(context, listen: false);
              reviewsService.reportReview(review.id);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال البلاغ بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('إبلاغ'),
          ),
        ],
      ),
    );
  }
}
