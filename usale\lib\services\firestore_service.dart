import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/product_model.dart';
import '../models/user_model.dart';
import '../models/chat_conversation_model.dart';
import '../models/message_model.dart';
import '../models/review_model.dart';
import '../models/notification_model.dart';

class FirestoreService {
  // Singleton pattern
  static final FirestoreService _instance = FirestoreService._internal();
  factory FirestoreService() => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  CollectionReference get users => _firestore.collection('users');
  CollectionReference get products => _firestore.collection('products');
  CollectionReference get categories => _firestore.collection('categories');
  CollectionReference get chats => _firestore.collection('chats');
  CollectionReference get reviews => _firestore.collection('reviews');
  CollectionReference get notifications => _firestore.collection('notifications');
  CollectionReference get favorites => _firestore.collection('favorites');
  CollectionReference get transactions => _firestore.collection('transactions');

  // Products methods
  Future<List<ProductModel>> getProducts({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? category,
    String? searchQuery,
  }) async {
    try {
      Query query = products.orderBy('created_at', descending: true);

      if (category != null && category.isNotEmpty) {
        query = query.where('category', isEqualTo: category);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('title', isGreaterThanOrEqualTo: searchQuery)
            .where('title', isLessThanOrEqualTo: '$searchQuery\uf8ff');
      }

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }).toList();
    } catch (e) {
      debugPrint('Error getting products: $e');
      return [];
    }
  }

  Future<ProductModel?> getProduct(String productId) async {
    try {
      final doc = await products.doc(productId).get();
      if (doc.exists) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }
      return null;
    } catch (e) {
      debugPrint('Error getting product: $e');
      return null;
    }
  }

  Future<String?> addProduct(ProductModel product) async {
    try {
      final docRef = await products.add(product.toJson());
      return docRef.id;
    } catch (e) {
      debugPrint('Error adding product: $e');
      return null;
    }
  }

  Future<bool> updateProduct(ProductModel product) async {
    try {
      await products.doc(product.id).update(product.toJson());
      return true;
    } catch (e) {
      debugPrint('Error updating product: $e');
      return false;
    }
  }

  Future<bool> deleteProduct(String productId) async {
    try {
      await products.doc(productId).delete();
      return true;
    } catch (e) {
      debugPrint('Error deleting product: $e');
      return false;
    }
  }

  // User methods
  Future<UserModel?> getUser(String userId) async {
    try {
      final doc = await users.doc(userId).get();
      if (doc.exists) {
        return UserModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  Future<bool> updateUser(UserModel user) async {
    try {
      await users.doc(user.id).set(user.toJson(), SetOptions(merge: true));
      return true;
    } catch (e) {
      debugPrint('Error updating user: $e');
      return false;
    }
  }

  // Chat methods
  Future<List<ChatModel>> getUserChats(String userId) async {
    try {
      final snapshot = await chats
          .where('participants', arrayContains: userId)
          .orderBy('last_message_at', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        return ChatModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }).toList();
    } catch (e) {
      debugPrint('Error getting user chats: $e');
      return [];
    }
  }

  Future<String?> createChat(ChatModel chat) async {
    try {
      final docRef = await chats.add(chat.toJson());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating chat: $e');
      return null;
    }
  }

  Future<bool> updateChat(ChatModel chat) async {
    try {
      await chats.doc(chat.id).update(chat.toJson());
      return true;
    } catch (e) {
      debugPrint('Error updating chat: $e');
      return false;
    }
  }

  // Messages methods
  Stream<List<MessageModel>> getChatMessages(String chatId) {
    return chats
        .doc(chatId)
        .collection('messages')
        .orderBy('created_at', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return MessageModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  Future<String?> sendMessage(String chatId, MessageModel message) async {
    try {
      final docRef = await chats
          .doc(chatId)
          .collection('messages')
          .add(message.toJson());
      
      // Update chat's last message
      await chats.doc(chatId).update({
        'last_message': message.content,
        'last_message_at': message.createdAt.toIso8601String(),
        'last_message_type': message.type.toString().split('.').last,
      });

      return docRef.id;
    } catch (e) {
      debugPrint('Error sending message: $e');
      return null;
    }
  }

  // Reviews methods
  Future<List<ReviewModel>> getReviews({
    required String targetId,
    required ReviewType type,
    int limit = 20,
  }) async {
    try {
      final snapshot = await reviews
          .where('target_id', isEqualTo: targetId)
          .where('type', isEqualTo: type.toString().split('.').last)
          .orderBy('created_at', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        return ReviewModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }).toList();
    } catch (e) {
      debugPrint('Error getting reviews: $e');
      return [];
    }
  }

  Future<String?> addReview(ReviewModel review) async {
    try {
      final docRef = await reviews.add(review.toJson());
      return docRef.id;
    } catch (e) {
      debugPrint('Error adding review: $e');
      return null;
    }
  }

  // Notifications methods
  Stream<List<NotificationModel>> getUserNotifications(String userId) {
    return notifications
        .where('user_id', isEqualTo: userId)
        .orderBy('created_at', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return NotificationModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }).toList();
    });
  }

  Future<String?> addNotification(NotificationModel notification) async {
    try {
      final docRef = await notifications.add(notification.toJson());
      return docRef.id;
    } catch (e) {
      debugPrint('Error adding notification: $e');
      return null;
    }
  }

  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await notifications.doc(notificationId).update({'is_read': true});
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Favorites methods
  Future<List<String>> getUserFavorites(String userId) async {
    try {
      final snapshot = await favorites
          .where('user_id', isEqualTo: userId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return data['product_id'] as String;
      }).toList();
    } catch (e) {
      debugPrint('Error getting user favorites: $e');
      return [];
    }
  }

  Future<bool> addToFavorites(String userId, String productId) async {
    try {
      await favorites.add({
        'user_id': userId,
        'product_id': productId,
        'created_at': DateTime.now().toIso8601String(),
      });
      return true;
    } catch (e) {
      debugPrint('Error adding to favorites: $e');
      return false;
    }
  }

  Future<bool> removeFromFavorites(String userId, String productId) async {
    try {
      final snapshot = await favorites
          .where('user_id', isEqualTo: userId)
          .where('product_id', isEqualTo: productId)
          .get();

      for (final doc in snapshot.docs) {
        await doc.reference.delete();
      }
      return true;
    } catch (e) {
      debugPrint('Error removing from favorites: $e');
      return false;
    }
  }

  // Search methods
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      // Simple text search - in production, use Algolia or similar
      final snapshot = await products
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(20)
          .get();

      return snapshot.docs.map((doc) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }).toList();
    } catch (e) {
      debugPrint('Error searching products: $e');
      return [];
    }
  }

  // Batch operations
  Future<bool> batchWrite(List<Map<String, dynamic>> operations) async {
    try {
      final batch = _firestore.batch();

      for (final operation in operations) {
        final type = operation['type'] as String;
        final collection = operation['collection'] as String;
        final docId = operation['docId'] as String?;
        final data = operation['data'] as Map<String, dynamic>;

        final docRef = docId != null
            ? _firestore.collection(collection).doc(docId)
            : _firestore.collection(collection).doc();

        switch (type) {
          case 'set':
            batch.set(docRef, data);
            break;
          case 'update':
            batch.update(docRef, data);
            break;
          case 'delete':
            batch.delete(docRef);
            break;
        }
      }

      await batch.commit();
      return true;
    } catch (e) {
      debugPrint('Error in batch write: $e');
      return false;
    }
  }
}
