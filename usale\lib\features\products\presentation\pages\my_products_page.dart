import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';
import 'add_product_page.dart';

class MyProductsPage extends StatefulWidget {
  const MyProductsPage({super.key});

  @override
  State<MyProductsPage> createState() => _MyProductsPageState();
}

class _MyProductsPageState extends State<MyProductsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<ProductModel> _activeProducts = [];
  List<ProductModel> _soldProducts = [];
  List<ProductModel> _expiredProducts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadProducts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadProducts() {
    // Simulate loading
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _activeProducts = [
          ProductModel(
            id: '1',
            title: 'آيفون 15 برو ماكس',
            description: 'جهاز جديد بالكرتون',
            price: 4500,
            category: 'إلكترونيات',
            condition: 'جديد',
            location: 'الرياض',
            sellerId: 'user1',
            sellerName: 'أحمد محمد',
            createdAt: DateTime.now().subtract(const Duration(hours: 2)),
            status: ProductStatus.available,
            viewsCount: 45,
            favoritesCount: 12,
          ),
          ProductModel(
            id: '2',
            title: 'لابتوب ديل XPS',
            description: 'لابتوب للبيع بحالة ممتازة',
            price: 3200,
            category: 'إلكترونيات',
            condition: 'مستعمل - ممتاز',
            location: 'الرياض',
            sellerId: 'user1',
            sellerName: 'أحمد محمد',
            createdAt: DateTime.now().subtract(const Duration(days: 1)),
            status: ProductStatus.available,
            viewsCount: 23,
            favoritesCount: 5,
          ),
        ];
        
        _soldProducts = [
          ProductModel(
            id: '3',
            title: 'ساعة أبل الجيل الثامن',
            description: 'ساعة ذكية مباعة',
            price: 1200,
            category: 'إلكترونيات',
            condition: 'مستعمل - جيد',
            location: 'الرياض',
            sellerId: 'user1',
            sellerName: 'أحمد محمد',
            createdAt: DateTime.now().subtract(const Duration(days: 3)),
            status: ProductStatus.sold,
            viewsCount: 67,
            favoritesCount: 18,
          ),
        ];
        
        _expiredProducts = [
          ProductModel(
            id: '4',
            title: 'دراجة هوائية',
            description: 'إعلان منتهي الصلاحية',
            price: 800,
            category: 'رياضة',
            condition: 'مستعمل - جيد',
            location: 'الرياض',
            sellerId: 'user1',
            sellerName: 'أحمد محمد',
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
            status: ProductStatus.expired,
            viewsCount: 12,
            favoritesCount: 2,
          ),
        ];
        
        _isLoading = false;
      });
    });
  }

  void _deleteProduct(ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: Text('هل أنت متأكد من حذف "${product.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _activeProducts.removeWhere((p) => p.id == product.id);
                _soldProducts.removeWhere((p) => p.id == product.id);
                _expiredProducts.removeWhere((p) => p.id == product.id);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف المنتج بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _editProduct(ProductModel product) {
    // Navigate to edit product page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddProductPage(), // In real app, pass product for editing
      ),
    );
  }

  void _markAsSold(ProductModel product) {
    setState(() {
      _activeProducts.removeWhere((p) => p.id == product.id);
      _soldProducts.add(product.copyWith(status: ProductStatus.sold));
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديد المنتج كمباع'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('منتجاتي'),
        backgroundColor: AppColors.surface,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('نشط'),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: const BoxDecoration(
                      color: AppColors.success,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      _activeProducts.length.toString(),
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('مباع'),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      _soldProducts.length.toString(),
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('منتهي'),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: const BoxDecoration(
                      color: AppColors.textLight,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      _expiredProducts.length.toString(),
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildProductsList(_activeProducts, ProductStatus.available),
                _buildProductsList(_soldProducts, ProductStatus.sold),
                _buildProductsList(_expiredProducts, ProductStatus.expired),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddProductPage(),
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildProductsList(List<ProductModel> products, ProductStatus status) {
    if (products.isEmpty) {
      return _buildEmptyState(status);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return _buildProductCard(product, status);
      },
    );
  }

  Widget _buildEmptyState(ProductStatus status) {
    String title;
    String subtitle;
    IconData icon;

    switch (status) {
      case ProductStatus.available:
        title = 'لا توجد منتجات نشطة';
        subtitle = 'ابدأ بإضافة منتج جديد للبيع';
        icon = Icons.add_box_outlined;
        break;
      case ProductStatus.sold:
        title = 'لا توجد منتجات مباعة';
        subtitle = 'ستظهر هنا المنتجات التي تم بيعها';
        icon = Icons.shopping_bag_outlined;
        break;
      case ProductStatus.expired:
        title = 'لا توجد منتجات منتهية الصلاحية';
        subtitle = 'ستظهر هنا المنتجات المنتهية الصلاحية';
        icon = Icons.schedule_outlined;
        break;
      default:
        title = 'لا توجد منتجات';
        subtitle = '';
        icon = Icons.inventory_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              icon,
              size: 60,
              color: AppColors.textLight,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (status == ProductStatus.available) ...[
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddProductPage(),
                  ),
                );
              },
              child: const Text('إضافة منتج جديد'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductCard(ProductModel product, ProductStatus status) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Product Image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.image,
                    size: 32,
                    color: AppColors.textLight,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Product Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product.formattedPrice,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product.timeAgo,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(status),
                    style: TextStyle(
                      color: _getStatusColor(status),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Stats
            Row(
              children: [
                _buildStatItem(Icons.visibility, '${product.viewsCount} مشاهدة'),
                const SizedBox(width: 16),
                _buildStatItem(Icons.favorite, '${product.favoritesCount} إعجاب'),
              ],
            ),
            const SizedBox(height: 12),
            
            // Actions
            Row(
              children: [
                if (status == ProductStatus.available) ...[
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _editProduct(product),
                      child: const Text('تعديل'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _markAsSold(product),
                      child: const Text('تم البيع'),
                    ),
                  ),
                ] else ...[
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _deleteProduct(product),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.error,
                        side: const BorderSide(color: AppColors.error),
                      ),
                      child: const Text('حذف'),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textLight),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(ProductStatus status) {
    switch (status) {
      case ProductStatus.available:
        return AppColors.success;
      case ProductStatus.sold:
        return AppColors.primary;
      case ProductStatus.expired:
        return AppColors.textLight;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(ProductStatus status) {
    switch (status) {
      case ProductStatus.available:
        return 'نشط';
      case ProductStatus.sold:
        return 'مباع';
      case ProductStatus.expired:
        return 'منتهي';
      default:
        return 'غير معروف';
    }
  }
}
