import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';
import '../../../../models/location_model.dart';
import '../../../../services/location_service.dart';
import '../../../../services/products_service.dart';
import '../widgets/map_product_card.dart';
import '../widgets/map_filter_sheet.dart';

class MapPage extends StatefulWidget {
  final String? categoryId;
  final String? searchQuery;

  const MapPage({
    super.key,
    this.categoryId,
    this.searchQuery,
  });

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  List<ProductModel> _products = [];
  ProductModel? _selectedProduct;
  bool _isLoading = true;
  String _mapStyle = '';
  
  // Default location (Riyadh)
  static const LatLng _defaultLocation = LatLng(24.7136, 46.6753);
  LatLng _currentLocation = _defaultLocation;
  
  // Filter options
  double _maxDistance = 50.0; // km
  double _minPrice = 0;
  double _maxPrice = 10000;
  String _selectedCategory = 'الكل';

  @override
  void initState() {
    super.initState();
    _loadMapStyle();
    _getCurrentLocation();
    _loadProducts();
  }

  Future<void> _loadMapStyle() async {
    _mapStyle = await DefaultAssetBundle.of(context)
        .loadString('assets/map_styles/map_style.json')
        .catchError((error) => '');
  }

  Future<void> _getCurrentLocation() async {
    try {
      final locationService = Provider.of<LocationService>(context, listen: false);
      final position = await locationService.getCurrentLocation();
      
      if (position != null && mounted) {
        setState(() {
          _currentLocation = LatLng(position.latitude, position.longitude);
        });
        
        _mapController?.animateCamera(
          CameraUpdate.newLatLng(_currentLocation),
        );
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
    }
  }

  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final productsService = Provider.of<ProductsService>(context, listen: false);
      
      // Load products with location data
      final products = await productsService.getProductsNearLocation(
        _currentLocation.latitude,
        _currentLocation.longitude,
        radiusKm: _maxDistance,
        categoryId: widget.categoryId,
        searchQuery: widget.searchQuery,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
      );

      if (mounted) {
        setState(() {
          _products = products;
          _isLoading = false;
        });
        _updateMarkers();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المنتجات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _updateMarkers() {
    final markers = <Marker>{};
    
    // Add current location marker
    markers.add(
      Marker(
        markerId: const MarkerId('current_location'),
        position: _currentLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: const InfoWindow(
          title: 'موقعك الحالي',
        ),
      ),
    );

    // Add product markers
    for (final product in _products) {
      if (product.location != null) {
        markers.add(
          Marker(
            markerId: MarkerId(product.id),
            position: LatLng(
              product.location!.latitude,
              product.location!.longitude,
            ),
            icon: BitmapDescriptor.defaultMarkerWithHue(
              _getMarkerColor(product.category),
            ),
            infoWindow: InfoWindow(
              title: product.title,
              snippet: '${product.price} ريال',
            ),
            onTap: () => _selectProduct(product),
          ),
        );
      }
    }

    setState(() {
      _markers = markers;
    });
  }

  double _getMarkerColor(String category) {
    switch (category.toLowerCase()) {
      case 'سيارات':
        return BitmapDescriptor.hueRed;
      case 'عقارات':
        return BitmapDescriptor.hueGreen;
      case 'إلكترونيات':
        return BitmapDescriptor.hueOrange;
      case 'أزياء':
        return BitmapDescriptor.hueMagenta;
      case 'رياضة':
        return BitmapDescriptor.hueYellow;
      default:
        return BitmapDescriptor.hueRed;
    }
  }

  void _selectProduct(ProductModel product) {
    setState(() {
      _selectedProduct = product;
    });
    
    // Animate to product location
    if (product.location != null) {
      _mapController?.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(product.location!.latitude, product.location!.longitude),
          15,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('الخريطة'),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
          ),
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _getCurrentLocation,
          ),
        ],
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: (GoogleMapController controller) {
              _mapController = controller;
              if (_mapStyle.isNotEmpty) {
                controller.setMapStyle(_mapStyle);
              }
            },
            initialCameraPosition: CameraPosition(
              target: _currentLocation,
              zoom: 12,
            ),
            markers: _markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            onTap: (_) {
              setState(() {
                _selectedProduct = null;
              });
            },
          ),

          // Loading indicator
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // Products count indicator
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${_products.length} منتج',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Selected product card
          if (_selectedProduct != null)
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: MapProductCard(
                product: _selectedProduct!,
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    '/product-details',
                    arguments: _selectedProduct,
                  );
                },
                onClose: () {
                  setState(() {
                    _selectedProduct = null;
                  });
                },
              ),
            ),

          // Map type toggle
          Positioned(
            top: 16,
            right: 16,
            child: Column(
              children: [
                FloatingActionButton(
                  mini: true,
                  heroTag: 'map_type',
                  onPressed: _toggleMapType,
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textPrimary,
                  child: const Icon(Icons.layers),
                ),
                const SizedBox(height: 8),
                FloatingActionButton(
                  mini: true,
                  heroTag: 'zoom_in',
                  onPressed: _zoomIn,
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textPrimary,
                  child: const Icon(Icons.add),
                ),
                const SizedBox(height: 8),
                FloatingActionButton(
                  mini: true,
                  heroTag: 'zoom_out',
                  onPressed: _zoomOut,
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textPrimary,
                  child: const Icon(Icons.remove),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MapFilterSheet(
        maxDistance: _maxDistance,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        selectedCategory: _selectedCategory,
        onApplyFilters: (distance, minPrice, maxPrice, category) {
          setState(() {
            _maxDistance = distance;
            _minPrice = minPrice;
            _maxPrice = maxPrice;
            _selectedCategory = category;
          });
          _loadProducts();
        },
      ),
    );
  }

  void _toggleMapType() {
    // Toggle between normal and satellite view
    // This would require additional implementation
  }

  void _zoomIn() {
    _mapController?.animateCamera(CameraUpdate.zoomIn());
  }

  void _zoomOut() {
    _mapController?.animateCamera(CameraUpdate.zoomOut());
  }
}
