import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/faq_section.dart';
import '../widgets/help_category_card.dart';
import '../widgets/contact_support_card.dart';

class HelpPage extends StatefulWidget {
  const HelpPage({super.key});

  @override
  State<HelpPage> createState() => _HelpPageState();
}

class _HelpPageState extends State<HelpPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('المساعدة والدعم'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'كيف يمكننا مساعدتك؟',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'ابحث عن إجابات للأسئلة الشائعة أو تواصل مع فريق الدعم',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Search Bar
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث في المساعدة...',
                      prefixIcon: const Icon(Icons.search),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Quick Help Categories
            const Text(
              'فئات المساعدة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                HelpCategoryCard(
                  title: 'البيع والشراء',
                  icon: Icons.shopping_cart,
                  color: AppColors.primary,
                  onTap: () => _navigateToCategory('buying_selling'),
                ),
                HelpCategoryCard(
                  title: 'الحساب والأمان',
                  icon: Icons.security,
                  color: AppColors.success,
                  onTap: () => _navigateToCategory('account_security'),
                ),
                HelpCategoryCard(
                  title: 'الدفع والفواتير',
                  icon: Icons.payment,
                  color: AppColors.warning,
                  onTap: () => _navigateToCategory('payment_billing'),
                ),
                HelpCategoryCard(
                  title: 'التطبيق والتقنية',
                  icon: Icons.phone_android,
                  color: AppColors.info,
                  onTap: () => _navigateToCategory('app_technical'),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // FAQ Section
            const Text(
              'الأسئلة الشائعة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            FAQSection(searchQuery: _searchQuery),

            const SizedBox(height: 32),

            // Contact Support
            const Text(
              'تحتاج مساعدة إضافية؟',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const ContactSupportCard(),

            const SizedBox(height: 32),

            // Quick Links
            const Text(
              'روابط مفيدة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildQuickLinks(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickLinks() {
    final links = [
      {
        'title': 'شروط الاستخدام',
        'icon': Icons.description,
        'route': '/terms-of-service',
      },
      {
        'title': 'سياسة الخصوصية',
        'icon': Icons.privacy_tip,
        'route': '/privacy-policy',
      },
      {
        'title': 'سياسة الإرجاع',
        'icon': Icons.assignment_return,
        'route': '/return-policy',
      },
      {
        'title': 'دليل البائع',
        'icon': Icons.store,
        'route': '/seller-guide',
      },
      {
        'title': 'دليل المشتري',
        'icon': Icons.shopping_bag,
        'route': '/buyer-guide',
      },
      {
        'title': 'نصائح الأمان',
        'icon': Icons.shield,
        'route': '/safety-tips',
      },
    ];

    return Column(
      children: links.map((link) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Card(
            child: ListTile(
              leading: Icon(
                link['icon'] as IconData,
                color: AppColors.primary,
              ),
              title: Text(link['title'] as String),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textLight,
              ),
              onTap: () {
                Navigator.pushNamed(context, link['route'] as String);
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  void _navigateToCategory(String category) {
    Navigator.pushNamed(
      context,
      '/help-category',
      arguments: category,
    );
  }
}
