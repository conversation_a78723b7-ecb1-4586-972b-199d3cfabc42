import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';
import '../../../../services/favorites_service.dart';
import '../../../products/presentation/pages/product_details_page.dart';

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> with AutomaticKeepAliveClientMixin {
  List<ProductModel> _favorites = [];
  bool _isLoading = true;
  final FavoritesService _favoritesService = FavoritesService();

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  void _loadFavorites() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final favorites = await _favoritesService.getFavorites();
      setState(() {
        _favorites = favorites.isNotEmpty ? favorites : [
          ProductModel(
            id: '1',
            title: 'آيفون 15 برو ماكس 256 جيجا',
            description: 'جهاز جديد بالكرتون، لون تيتانيوم طبيعي',
            price: 4500,
            category: 'إلكترونيات',
            condition: 'جديد',
            location: 'الرياض',
            sellerId: 'seller1',
            sellerName: 'أحمد محمد',
            createdAt: DateTime.now().subtract(const Duration(hours: 2)),
            isFeatured: true,
          ),
          ProductModel(
            id: '2',
            title: 'سيارة تويوتا كامري 2022',
            description: 'سيارة نظيفة، صيانة منتظمة، بحالة ممتازة',
            price: 85000,
            category: 'سيارات',
            condition: 'مستعمل - ممتاز',
            location: 'جدة',
            sellerId: 'seller2',
            sellerName: 'محمد علي',
            createdAt: DateTime.now().subtract(const Duration(hours: 5)),
            isFeatured: false,
          ),
          ProductModel(
            id: '3',
            title: 'شقة للإيجار 3 غرف وصالة',
            description: 'شقة مفروشة بالكامل، موقع مميز',
            price: 2500,
            category: 'عقارات',
            condition: 'جديد',
            location: 'الدمام',
            sellerId: 'seller3',
            sellerName: 'فاطمة أحمد',
            createdAt: DateTime.now().subtract(const Duration(days: 1)),
            isFeatured: true,
          ),
        ];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _removeFavorite(String id) async {
    final success = await _favoritesService.removeFromFavorites(id);
    if (success) {
      setState(() {
        _favorites.removeWhere((item) => item.id == id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إزالة العنصر من المفضلة'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }

  void _clearAllFavorites() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلة'),
        content: const Text('هل أنت متأكد من مسح جميع العناصر المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();
              try {
                await _favoritesService.clearFavorites();
                if (mounted) {
                  setState(() {
                    _favorites.clear();
                  });
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم مسح جميع المفضلة'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في مسح المفضلة: $e'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('المفضلة'),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          if (_favorites.isNotEmpty)
            IconButton(
              onPressed: _clearAllFavorites,
              icon: const Icon(Icons.clear_all),
              tooltip: 'مسح الكل',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _favorites.isEmpty
              ? _buildEmptyState()
              : _buildFavoritesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.favorite_outline,
              size: 60,
              color: AppColors.textLight,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد عناصر مفضلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة المنتجات التي تعجبك إلى المفضلة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              // Navigate to home or search
            },
            child: const Text('تصفح المنتجات'),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _favorites.length,
      itemBuilder: (context, index) {
        final item = _favorites[index];
        return _buildFavoriteItem(item);
      },
    );
  }

  Widget _buildFavoriteItem(ProductModel item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailsPage(product: item),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
          children: [
            // Product Image
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.lightGrey,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.image,
                color: AppColors.textLight,
                size: 32,
              ),
            ),
            const SizedBox(width: 12),
            
            // Product Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (item.isFeatured)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.accent,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'مميز',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.formattedPrice,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: AppColors.textLight,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        item.location,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Actions
            Column(
              children: [
                IconButton(
                  onPressed: () => _removeFavorite(item.id),
                  icon: const Icon(
                    Icons.favorite,
                    color: AppColors.accent,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // Share item
                  },
                  icon: const Icon(
                    Icons.share_outlined,
                    color: AppColors.textLight,
                  ),
                ),
              ],
            ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}


