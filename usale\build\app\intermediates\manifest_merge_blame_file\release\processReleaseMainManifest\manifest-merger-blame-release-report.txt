1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.usale.marketplace"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Essential permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:5:5-67
12-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:6:5-79
13-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:7:5-76
14-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:7:22-73
15
16    <!-- Phone permissions -->
17    <uses-permission android:name="android.permission.CALL_PHONE" />
17-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:10:5-69
17-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:10:22-66
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:11:5-75
18-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:11:22-72
19
20    <!-- Storage permissions -->
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:14:5-80
21-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission
22-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:15:5-16:38
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:15:22-78
24        android:maxSdkVersion="28" />
24-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:16:9-35
25
26    <!-- Camera permission -->
27    <uses-permission android:name="android.permission.CAMERA" />
27-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:19:5-65
27-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:19:22-62
28
29    <!-- Location permissions -->
30    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
30-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:22:5-79
30-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:22:22-76
31    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
31-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:23:5-81
31-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:23:22-78
32
33    <!-- Notification permissions -->
34    <uses-permission android:name="android.permission.VIBRATE" />
34-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:26:5-66
34-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:26:22-63
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:27:5-68
35-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:27:22-65
36
37    <!-- For Android 13+ -->
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
38-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:30:5-77
38-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:30:22-74
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.usale.marketplace.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.usale.marketplace.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
46-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:32:5-66:19
47        android:name="com.usale.marketplace.MainApplication"
47-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:34:9-40
48        android:allowBackup="true"
48-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:36:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:extractNativeLibs="true"
51        android:hardwareAccelerated="true"
51-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:39:9-43
52        android:icon="@mipmap/ic_launcher"
52-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:35:9-43
53        android:label="يوسيل - USale"
53-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:33:9-38
54        android:largeHeap="true"
54-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:40:9-33
55        android:requestLegacyExternalStorage="true"
55-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:37:9-52
56        android:theme="@android:style/Theme.Light.NoTitleBar"
56-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:41:9-62
57        android:usesCleartextTraffic="true" >
57-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:38:9-44
58        <activity
58-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:43:9-61:20
59            android:name="com.usale.marketplace.MainActivity"
59-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:44:13-41
60            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
60-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:49:13-163
61            android:exported="true"
61-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:45:13-36
62            android:hardwareAccelerated="true"
62-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:50:13-47
63            android:launchMode="singleTop"
63-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:46:13-43
64            android:taskAffinity=""
64-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:47:13-36
65            android:theme="@style/LaunchTheme"
65-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:48:13-47
66            android:windowSoftInputMode="adjustResize" >
66-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:51:13-55
67            <meta-data
67-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:53:13-55:55
68                android:name="io.flutter.embedding.android.NormalTheme"
68-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:54:15-70
69                android:resource="@style/NormalTheme" />
69-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:55:15-52
70
71            <intent-filter>
71-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:57:13-60:29
72                <action android:name="android.intent.action.MAIN" />
72-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:58:17-68
72-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:58:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:59:17-76
74-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:59:27-74
75            </intent-filter>
76        </activity>
77
78        <meta-data
78-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:63:9-65:33
79            android:name="flutterEmbedding"
79-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:64:13-44
80            android:value="2" />
80-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:65:13-30
81
82        <provider
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
83            android:name="androidx.startup.InitializationProvider"
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
84            android:authorities="com.usale.marketplace.androidx-startup"
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
85            android:exported="false" >
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
86            <meta-data
86-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.emoji2.text.EmojiCompatInitializer"
87-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
88                android:value="androidx.startup" />
88-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
92            <meta-data
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
94                android:value="androidx.startup" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
95        </provider>
96
97        <uses-library
97-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
98            android:name="androidx.window.extensions"
98-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
99            android:required="false" />
99-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
100        <uses-library
100-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
101            android:name="androidx.window.sidecar"
101-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
102            android:required="false" />
102-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
103
104        <receiver
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
105            android:name="androidx.profileinstaller.ProfileInstallReceiver"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
106            android:directBootAware="false"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
107            android:enabled="true"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
108            android:exported="true"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
109            android:permission="android.permission.DUMP" >
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
111                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
114                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
117                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
118            </intent-filter>
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
120                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
121            </intent-filter>
122        </receiver>
123    </application>
124
125</manifest>
