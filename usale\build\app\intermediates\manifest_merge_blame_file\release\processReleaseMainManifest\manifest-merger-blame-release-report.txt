1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.usale.marketplace"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!-- Required permissions -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:4:5-79
12-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:5:5-68
13-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:5:22-65
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:6:5-79
14-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:6:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:7:5-81
15-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:7:22-78
16    <uses-permission android:name="android.permission.CAMERA" />
16-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:8:5-65
16-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:8:22-62
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:9:5-80
17-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:9:22-77
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:10:5-81
18-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:10:22-78
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:11:5-66
19-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:11:22-63
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:12:5-81
20-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:12:22-78
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:13:5-77
21-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:13:22-74
22    <!--
23         Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:51:5-56:15
30        <intent>
30-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:52:9-55:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:53:13-72
31-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:53:21-70
32
33            <data android:mimeType="text/plain" />
33-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:54:13-50
33-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:54:19-48
34        </intent>
35    </queries>
36
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Usale\usale\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
37-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Usale\usale\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
38
39    <permission
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.usale.marketplace.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.usale.marketplace.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44
45    <application
46        android:name="android.app.Application"
46-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:16:9-42
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:extractNativeLibs="true"
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:17:9-43
50        android:label="يو سيل - USale" >
50-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:15:9-39
51        <activity
51-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:18:9-39:20
52            android:name="com.usale.marketplace.MainActivity"
52-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:19:13-41
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
53-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:24:13-163
54            android:exported="true"
54-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:20:13-36
55            android:hardwareAccelerated="true"
55-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:25:13-47
56            android:launchMode="singleTop"
56-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:21:13-43
57            android:taskAffinity=""
57-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:22:13-36
58            android:theme="@style/LaunchTheme"
58-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:23:13-47
59            android:windowSoftInputMode="adjustResize" >
59-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:26:13-55
60
61            <!--
62                 Specifies an Android theme to apply to this Activity as soon as
63                 the Android process has started. This theme is visible to the user
64                 while the Flutter UI initializes. After that, this theme continues
65                 to determine the Window background behind the Flutter UI.
66            -->
67            <meta-data
67-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:31:13-34:17
68                android:name="io.flutter.embedding.android.NormalTheme"
68-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:32:15-70
69                android:resource="@style/NormalTheme" />
69-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:33:15-52
70
71            <intent-filter>
71-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:35:13-38:29
72                <action android:name="android.intent.action.MAIN" />
72-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:36:17-68
72-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:36:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:37:17-76
74-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:37:27-74
75            </intent-filter>
76        </activity>
77        <!--
78             Don't delete the meta-data below.
79             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
80        -->
81        <meta-data
81-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:42:9-44:33
82            android:name="flutterEmbedding"
82-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:43:13-44
83            android:value="2" />
83-->C:\Users\<USER>\Desktop\Usale\usale\android\app\src\main\AndroidManifest.xml:44:13-30
84        <!--
85           Declares a provider which allows us to store files to share in
86           '.../caches/share_plus' and grant the receiving action access
87        -->
88        <provider
88-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
89            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
89-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
90            android:authorities="com.usale.marketplace.flutter.share_provider"
90-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
91            android:exported="false"
91-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
92            android:grantUriPermissions="true" >
92-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
93            <meta-data
93-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
94                android:name="android.support.FILE_PROVIDER_PATHS"
94-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
95                android:resource="@xml/flutter_share_file_paths" />
95-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
96        </provider>
97        <!--
98           This manifest declared broadcast receiver allows us to use an explicit
99           Intent when creating a PendingItent to be informed of the user's choice
100        -->
101        <receiver
101-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
102            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
102-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
103            android:exported="false" >
103-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
104            <intent-filter>
104-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
105                <action android:name="EXTRA_CHOSEN_COMPONENT" />
105-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
105-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
106            </intent-filter>
107        </receiver>
108
109        <provider
109-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
110            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
110-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
111            android:authorities="com.usale.marketplace.flutter.image_provider"
111-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
112            android:exported="false"
112-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
113            android:grantUriPermissions="true" >
113-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
114            <meta-data
114-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
115                android:name="android.support.FILE_PROVIDER_PATHS"
115-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
116                android:resource="@xml/flutter_image_picker_file_paths" />
116-->[:share_plus] C:\Users\<USER>\Desktop\Usale\usale\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
117        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
118        <service
118-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
119            android:name="com.google.android.gms.metadata.ModuleDependencies"
119-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
120            android:enabled="false"
120-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
121            android:exported="false" >
121-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
122            <intent-filter>
122-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
123                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
123-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
123-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
124            </intent-filter>
125
126            <meta-data
126-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
127                android:name="photopicker_activity:0:required"
127-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
128                android:value="" />
128-->[:image_picker_android] C:\Users\<USER>\Desktop\Usale\usale\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
129        </service>
130
131        <activity
131-->[:url_launcher_android] C:\Users\<USER>\Desktop\Usale\usale\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
132            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
132-->[:url_launcher_android] C:\Users\<USER>\Desktop\Usale\usale\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
133            android:exported="false"
133-->[:url_launcher_android] C:\Users\<USER>\Desktop\Usale\usale\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
134            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
134-->[:url_launcher_android] C:\Users\<USER>\Desktop\Usale\usale\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
135        <activity
135-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
136            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
136-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
137            android:excludeFromRecents="true"
137-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
138            android:exported="false"
138-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
139            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
139-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
140        <!--
141            Service handling Google Sign-In user revocation. For apps that do not integrate with
142            Google Sign-In, this service will never be started.
143        -->
144        <service
144-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
145            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
145-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
146            android:exported="true"
146-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
147            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
147-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
148            android:visibleToInstantApps="true" />
148-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
149
150        <activity
150-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
151            android:name="com.google.android.gms.common.api.GoogleApiActivity"
151-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
152            android:exported="false"
152-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
153            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
153-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
154
155        <meta-data
155-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde491953245a6a32bc7896f31b86f18\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
156            android:name="com.google.android.gms.version"
156-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde491953245a6a32bc7896f31b86f18\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
157            android:value="@integer/google_play_services_version" />
157-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde491953245a6a32bc7896f31b86f18\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
158
159        <provider
159-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
160            android:name="androidx.startup.InitializationProvider"
160-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
161            android:authorities="com.usale.marketplace.androidx-startup"
161-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
162            android:exported="false" >
162-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
163            <meta-data
163-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
164                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
164-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
165                android:value="androidx.startup" />
165-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
166            <meta-data
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
168                android:value="androidx.startup" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
169        </provider>
170
171        <uses-library
171-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
172            android:name="androidx.window.extensions"
172-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
173            android:required="false" />
173-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
174        <uses-library
174-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
175            android:name="androidx.window.sidecar"
175-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
176            android:required="false" />
176-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
177
178        <receiver
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
179            android:name="androidx.profileinstaller.ProfileInstallReceiver"
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
180            android:directBootAware="false"
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
181            android:enabled="true"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
182            android:exported="true"
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
183            android:permission="android.permission.DUMP" >
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
184            <intent-filter>
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
185                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
186            </intent-filter>
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
188                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
189            </intent-filter>
190            <intent-filter>
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
191                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
192            </intent-filter>
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
194                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
195            </intent-filter>
196        </receiver>
197    </application>
198
199</manifest>
