import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_model.dart';

class FirebaseAuthService extends ChangeNotifier {
  // Singleton pattern
  static final FirebaseAuthService _instance = FirebaseAuthService._internal();
  factory FirebaseAuthService() => _instance;
  FirebaseAuthService._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  UserModel? _currentUser;
  bool _isLoading = false;
  StreamSubscription<User?>? _authStateSubscription;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;
  User? get firebaseUser => _firebaseAuth.currentUser;

  // Initialize Firebase Auth
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Listen to auth state changes
      _authStateSubscription = _firebaseAuth.authStateChanges().listen(_onAuthStateChanged);
      
      // Check if user is already signed in
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await _loadUserData(user.uid);
      }
    } catch (e) {
      debugPrint('Firebase Auth initialization error: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Handle auth state changes
  void _onAuthStateChanged(User? user) async {
    if (user != null) {
      await _loadUserData(user.uid);
    } else {
      _currentUser = null;
      notifyListeners();
    }
  }

  // Load user data from Firestore
  Future<void> _loadUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        _currentUser = UserModel.fromJson({
          'id': uid,
          ...doc.data()!,
        });
      } else {
        // Create user document if it doesn't exist
        final user = _firebaseAuth.currentUser!;
        _currentUser = UserModel(
          id: uid,
          name: user.displayName ?? 'مستخدم جديد',
          email: user.email ?? '',
          profileImage: user.photoURL,
          createdAt: DateTime.now(),
          isVerified: user.emailVerified,
          userType: UserType.buyer,
          status: AccountStatus.active,
          settings: UserSettings(),
        );
        
        await _saveUserData();
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  // Save user data to Firestore
  Future<void> _saveUserData() async {
    if (_currentUser == null) return;
    
    try {
      await _firestore.collection('users').doc(_currentUser!.id).set({
        'name': _currentUser!.name,
        'email': _currentUser!.email,
        'phone': _currentUser!.phone,
        'profile_image': _currentUser!.profileImage,
        'bio': _currentUser!.bio,
        'location': _currentUser!.location,
        'created_at': _currentUser!.createdAt.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'last_login_at': _currentUser!.lastLoginAt?.toIso8601String(),
        'is_verified': _currentUser!.isVerified,
        'user_type': _currentUser!.userType.toString().split('.').last,
        'status': _currentUser!.status.toString().split('.').last,
        'settings': _currentUser!.settings.toJson(),
      });
    } catch (e) {
      debugPrint('Error saving user data: $e');
    }
  }

  // Sign in with email and password
  Future<AuthResult> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading = true;
      notifyListeners();

      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserData(credential.user!.uid);
        return AuthResult.success('تم تسجيل الدخول بنجاح');
      } else {
        return AuthResult.error('فشل في تسجيل الدخول');
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception in signIn: ${e.code} - ${e.message}');
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      debugPrint('Unknown error in signIn: $e');
      return AuthResult.error('حدث خطأ غير متوقع');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create account with email and password
  Future<AuthResult> createUserWithEmailAndPassword({
    required String name,
    required String email,
    required String password,
    String? phone,
    UserType userType = UserType.buyer,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(name);
        
        // Create user model
        _currentUser = UserModel(
          id: credential.user!.uid,
          name: name,
          email: email,
          phone: phone,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isVerified: credential.user!.emailVerified,
          userType: userType,
          status: AccountStatus.active,
          settings: UserSettings(),
        );

        await _saveUserData();
        
        // Send email verification
        await credential.user!.sendEmailVerification();
        
        return AuthResult.success('تم إنشاء الحساب بنجاح. يرجى تأكيد البريد الإلكتروني');
      } else {
        return AuthResult.error('فشل في إنشاء الحساب');
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception in createUser: ${e.code} - ${e.message}');
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      debugPrint('Unknown error in createUser: $e');
      return AuthResult.error('حدث خطأ غير متوقع');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Sign in with Google
  Future<AuthResult> signInWithGoogle() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return AuthResult.error('تم إلغاء تسجيل الدخول');
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _loadUserData(userCredential.user!.uid);
        return AuthResult.success('تم تسجيل الدخول بنجاح باستخدام Google');
      } else {
        return AuthResult.error('فشل في تسجيل الدخول باستخدام Google');
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception in Google Sign In: ${e.code} - ${e.message}');
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      debugPrint('Unknown error in Google Sign In: $e');
      return AuthResult.error('حدث خطأ أثناء تسجيل الدخول باستخدام Google');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Sign in with phone number
  Future<AuthResult> signInWithPhoneNumber(String phoneNumber) async {
    try {
      _isLoading = true;
      notifyListeners();

      final completer = Completer<AuthResult>();

      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            final userCredential = await _firebaseAuth.signInWithCredential(credential);
            if (userCredential.user != null) {
              await _loadUserData(userCredential.user!.uid);
              completer.complete(AuthResult.success('تم تسجيل الدخول بنجاح'));
            } else {
              completer.complete(AuthResult.error('فشل في تسجيل الدخول'));
            }
          } catch (e) {
            completer.complete(AuthResult.error('حدث خطأ أثناء التحقق التلقائي'));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          completer.complete(AuthResult.error(_getFirebaseErrorMessage(e.code)));
        },
        codeSent: (String verificationId, int? resendToken) {
          completer.complete(AuthResult.success('تم إرسال رمز التحقق', {
            'verificationId': verificationId,
            'resendToken': resendToken,
          }));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Handle timeout
        },
      );

      return await completer.future;
    } catch (e) {
      debugPrint('Error in phone verification: $e');
      return AuthResult.error('حدث خطأ أثناء إرسال رمز التحقق');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Verify phone number with SMS code
  Future<AuthResult> verifyPhoneCode(String verificationId, String smsCode) async {
    try {
      _isLoading = true;
      notifyListeners();

      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: smsCode,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _loadUserData(userCredential.user!.uid);
        return AuthResult.success('تم تسجيل الدخول بنجاح');
      } else {
        return AuthResult.error('فشل في تسجيل الدخول');
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception in phone verification: ${e.code} - ${e.message}');
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      debugPrint('Unknown error in phone verification: $e');
      return AuthResult.error('حدث خطأ أثناء التحقق من الرمز');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
      ]);
      _currentUser = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }

  // Send password reset email
  Future<AuthResult> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return AuthResult.success('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      return AuthResult.error('حدث خطأ غير متوقع');
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile(UserModel updatedUser) async {
    try {
      if (_currentUser == null) {
        return AuthResult.error('المستخدم غير مسجل الدخول');
      }

      _currentUser = updatedUser.copyWith(updatedAt: DateTime.now());
      await _saveUserData();
      
      // Update Firebase Auth profile
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await user.updateDisplayName(updatedUser.name);
        if (updatedUser.profileImage != null) {
          await user.updatePhotoURL(updatedUser.profileImage);
        }
      }
      
      notifyListeners();
      return AuthResult.success('تم تحديث الملف الشخصي بنجاح');
    } catch (e) {
      return AuthResult.error('حدث خطأ أثناء تحديث الملف الشخصي');
    }
  }

  // Send email verification
  Future<AuthResult> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        return AuthResult.success('تم إرسال رابط التأكيد إلى بريدك الإلكتروني');
      } else {
        return AuthResult.error('البريد الإلكتروني مؤكد بالفعل');
      }
    } catch (e) {
      return AuthResult.error('حدث خطأ أثناء إرسال رابط التأكيد');
    }
  }

  // Reload user to check email verification
  Future<void> reloadUser() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await user.reload();
        if (_currentUser != null) {
          _currentUser = _currentUser!.copyWith(
            isVerified: user.emailVerified,
            updatedAt: DateTime.now(),
          );
          await _saveUserData();
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error reloading user: $e');
    }
  }

  // Get Firebase error message in Arabic
  String _getFirebaseErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'لا يوجد مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح. حاول لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'network-request-failed':
        return 'فشل في الاتصال بالشبكة';
      case 'invalid-credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'account-exists-with-different-credential':
        return 'يوجد حساب بهذا البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'requires-recent-login':
        return 'يتطلب تسجيل دخول حديث';
      case 'credential-already-in-use':
        return 'بيانات الاعتماد مستخدمة بالفعل';
      case 'invalid-verification-code':
        return 'رمز التحقق غير صحيح';
      case 'invalid-verification-id':
        return 'معرف التحقق غير صحيح';
      default:
        debugPrint('Unknown Firebase Auth error: $errorCode');
        return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى';
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;
  final dynamic data;

  AuthResult._(this.isSuccess, this.message, [this.data]);

  factory AuthResult.success(String message, [dynamic data]) {
    return AuthResult._(true, message, data);
  }

  factory AuthResult.error(String message) {
    return AuthResult._(false, message);
  }
}
