import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - ألوان أساسية عصرية
  static const Color primary = Color(0xFF6C5CE7);
  static const Color primaryLight = Color(0xFF8B7ED8);
  static const Color primaryDark = Color(0xFF5A4FCF);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF00CEC9);
  static const Color secondaryLight = Color(0xFF55E6E2);
  static const Color secondaryDark = Color(0xFF00A8A3);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF6B6B);
  static const Color accentLight = Color(0xFFFF8E8E);
  static const Color accentDark = Color(0xFFE55555);
  
  // Success & Status Colors
  static const Color success = Color(0xFF00B894);
  static const Color warning = Color(0xFFFFB347);
  static const Color error = Color(0xFFE17055);
  static const Color info = Color(0xFF74B9FF);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF2D3436);
  static const Color grey = Color(0xFF636E72);
  static const Color lightGrey = Color(0xFFDDD6FE);
  static const Color darkGrey = Color(0xFF2D3436);
  
  // Background Colors
  static const Color background = Color(0xFFF8F9FA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF1F3F4);
  
  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkSurfaceVariant = Color(0xFF2C2C2C);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF2D3436);
  static const Color textSecondary = Color(0xFF636E72);
  static const Color textLight = Color(0xFF95A5A6);
  static const Color textDark = Color(0xFFFFFFFF);
  
  // Category Colors
  static const Color categoryAutos = Color(0xFF3498DB);
  static const Color categoryRealEstate = Color(0xFF27AE60);
  static const Color categoryElectronics = Color(0xFF9B59B6);
  static const Color categoryFashion = Color(0xFFE91E63);
  static const Color categoryJobs = Color(0xFFFF9800);
  static const Color categoryServices = Color(0xFF795548);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
  static const Color shadow = Color(0x1F000000);

  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
}
