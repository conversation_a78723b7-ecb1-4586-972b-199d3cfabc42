enum ChatType {
  direct,
  group,
  support,
}

class ChatModel {
  final String id;
  final String participant1Id;
  final String participant2Id;
  final String participant1Name;
  final String participant2Name;
  final String? participant1Image;
  final String? participant2Image;
  final String? productId;
  final String? productTitle;
  final String? productImage;
  final String? lastMessage;
  final DateTime? lastMessageAt;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;
  final int unreadCount;
  final ChatType type;

  ChatModel({
    required this.id,
    required this.participant1Id,
    required this.participant2Id,
    required this.participant1Name,
    required this.participant2Name,
    this.participant1Image,
    this.participant2Image,
    this.productId,
    this.productTitle,
    this.productImage,
    this.lastMessage,
    this.lastMessageAt,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.unreadCount = 0,
    this.type = ChatType.direct,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] ?? '',
      participant1Id: json['participant1_id'] ?? '',
      participant2Id: json['participant2_id'] ?? '',
      participant1Name: json['participant1_name'] ?? '',
      participant2Name: json['participant2_name'] ?? '',
      participant1Image: json['participant1_image'],
      participant2Image: json['participant2_image'],
      productId: json['product_id'],
      productTitle: json['product_title'],
      productImage: json['product_image'],
      lastMessage: json['last_message'],
      lastMessageAt: json['last_message_at'] != null 
          ? DateTime.parse(json['last_message_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at']) 
          : null,
      isActive: json['is_active'] ?? true,
      unreadCount: json['unread_count'] ?? 0,
      type: ChatType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'direct'),
        orElse: () => ChatType.direct,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participant1_id': participant1Id,
      'participant2_id': participant2Id,
      'participant1_name': participant1Name,
      'participant2_name': participant2Name,
      'participant1_image': participant1Image,
      'participant2_image': participant2Image,
      'product_id': productId,
      'product_title': productTitle,
      'product_image': productImage,
      'last_message': lastMessage,
      'last_message_at': lastMessageAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_active': isActive,
      'unread_count': unreadCount,
      'type': type.toString().split('.').last,
    };
  }

  ChatModel copyWith({
    String? id,
    String? participant1Id,
    String? participant2Id,
    String? participant1Name,
    String? participant2Name,
    String? participant1Image,
    String? participant2Image,
    String? productId,
    String? productTitle,
    String? productImage,
    String? lastMessage,
    DateTime? lastMessageAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    int? unreadCount,
    ChatType? type,
  }) {
    return ChatModel(
      id: id ?? this.id,
      participant1Id: participant1Id ?? this.participant1Id,
      participant2Id: participant2Id ?? this.participant2Id,
      participant1Name: participant1Name ?? this.participant1Name,
      participant2Name: participant2Name ?? this.participant2Name,
      participant1Image: participant1Image ?? this.participant1Image,
      participant2Image: participant2Image ?? this.participant2Image,
      productId: productId ?? this.productId,
      productTitle: productTitle ?? this.productTitle,
      productImage: productImage ?? this.productImage,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      unreadCount: unreadCount ?? this.unreadCount,
      type: type ?? this.type,
    );
  }

  String get timeAgo {
    if (lastMessageAt == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(lastMessageAt!);
    
    if (difference.inDays > 7) {
      return '${lastMessageAt!.day}/${lastMessageAt!.month}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  String getOtherUserName(String currentUserId) {
    return participant1Id == currentUserId ? participant2Name : participant1Name;
  }

  String? getOtherUserImage(String currentUserId) {
    return participant1Id == currentUserId ? participant2Image : participant1Image;
  }

  String getOtherUserId(String currentUserId) {
    return participant1Id == currentUserId ? participant2Id : participant1Id;
  }

  @override
  String toString() {
    return 'ChatModel(id: $id, participant1: $participant1Name, participant2: $participant2Name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
