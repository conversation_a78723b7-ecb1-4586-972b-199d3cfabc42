class ChatItem {
  final String id;
  final String userName;
  final String userImage;
  final String lastMessage;
  final DateTime timestamp;
  final int unreadCount;
  final bool isOnline;
  final String productTitle;
  final String productId;

  ChatItem({
    required this.id,
    required this.userName,
    required this.userImage,
    required this.lastMessage,
    required this.timestamp,
    required this.unreadCount,
    required this.isOnline,
    required this.productTitle,
    required this.productId,
  });

  factory ChatItem.fromJson(Map<String, dynamic> json) {
    return ChatItem(
      id: json['id'] ?? '',
      userName: json['user_name'] ?? '',
      userImage: json['user_image'] ?? '',
      lastMessage: json['last_message'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      unreadCount: json['unread_count'] ?? 0,
      isOnline: json['is_online'] ?? false,
      productTitle: json['product_title'] ?? '',
      productId: json['product_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_name': userName,
      'user_image': userImage,
      'last_message': lastMessage,
      'timestamp': timestamp.toIso8601String(),
      'unread_count': unreadCount,
      'is_online': isOnline,
      'product_title': productTitle,
      'product_id': productId,
    };
  }

  ChatItem copyWith({
    String? id,
    String? userName,
    String? userImage,
    String? lastMessage,
    DateTime? timestamp,
    int? unreadCount,
    bool? isOnline,
    String? productTitle,
    String? productId,
  }) {
    return ChatItem(
      id: id ?? this.id,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      lastMessage: lastMessage ?? this.lastMessage,
      timestamp: timestamp ?? this.timestamp,
      unreadCount: unreadCount ?? this.unreadCount,
      isOnline: isOnline ?? this.isOnline,
      productTitle: productTitle ?? this.productTitle,
      productId: productId ?? this.productId,
    );
  }

  @override
  String toString() {
    return 'ChatItem(id: $id, userName: $userName, lastMessage: $lastMessage, unreadCount: $unreadCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class ChatMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String message;
  final DateTime timestamp;
  final bool isMe;
  final MessageType messageType;
  final MessageStatus status;
  final String? replyToId;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
    required this.isMe,
    this.messageType = MessageType.text,
    this.status = MessageStatus.sent,
    this.replyToId,
    this.metadata,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] ?? '',
      senderId: json['sender_id'] ?? '',
      senderName: json['sender_name'] ?? '',
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      isMe: json['is_me'] ?? false,
      messageType: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['message_type'] ?? 'text'),
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == (json['status'] ?? 'sent'),
        orElse: () => MessageStatus.sent,
      ),
      replyToId: json['reply_to_id'],
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'sender_name': senderName,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'is_me': isMe,
      'message_type': messageType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'reply_to_id': replyToId,
      'metadata': metadata,
    };
  }

  ChatMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? message,
    DateTime? timestamp,
    bool? isMe,
    MessageType? messageType,
    MessageStatus? status,
    String? replyToId,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isMe: isMe ?? this.isMe,
      messageType: messageType ?? this.messageType,
      status: status ?? this.status,
      replyToId: replyToId ?? this.replyToId,
      metadata: metadata ?? this.metadata,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  @override
  String toString() {
    return 'ChatMessage(id: $id, senderId: $senderId, message: $message, timestamp: $timestamp, isMe: $isMe)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum MessageType {
  text,
  image,
  video,
  audio,
  file,
  location,
  contact,
  sticker,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

class ChatRoom {
  final String id;
  final String name;
  final String? image;
  final List<String> participants;
  final ChatMessage? lastMessage;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isGroup;
  final Map<String, dynamic>? settings;

  ChatRoom({
    required this.id,
    required this.name,
    this.image,
    required this.participants,
    this.lastMessage,
    required this.createdAt,
    this.updatedAt,
    this.isGroup = false,
    this.settings,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) {
    return ChatRoom(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      image: json['image'],
      participants: List<String>.from(json['participants'] ?? []),
      lastMessage: json['last_message'] != null 
          ? ChatMessage.fromJson(json['last_message']) 
          : null,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at']) 
          : null,
      isGroup: json['is_group'] ?? false,
      settings: json['settings'] != null 
          ? Map<String, dynamic>.from(json['settings']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'participants': participants,
      'last_message': lastMessage?.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_group': isGroup,
      'settings': settings,
    };
  }

  ChatRoom copyWith({
    String? id,
    String? name,
    String? image,
    List<String>? participants,
    ChatMessage? lastMessage,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isGroup,
    Map<String, dynamic>? settings,
  }) {
    return ChatRoom(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      participants: participants ?? this.participants,
      lastMessage: lastMessage ?? this.lastMessage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isGroup: isGroup ?? this.isGroup,
      settings: settings ?? this.settings,
    );
  }

  @override
  String toString() {
    return 'ChatRoom(id: $id, name: $name, participants: $participants, isGroup: $isGroup)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatRoom && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
