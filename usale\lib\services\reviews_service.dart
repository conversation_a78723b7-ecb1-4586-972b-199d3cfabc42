import 'package:flutter/foundation.dart';
import '../models/review_model.dart';
// import '../services/database_service.dart'; // Removed unused import
import '../services/auth_service.dart';

class ReviewsService extends ChangeNotifier {
  // Singleton pattern
  static final ReviewsService _instance = ReviewsService._internal();
  factory ReviewsService() => _instance;
  ReviewsService._internal();

  // final DatabaseService _databaseService = DatabaseService(); // Removed unused field
  final AuthService _authService = AuthService();

  List<ReviewModel> _reviews = [];
  final Map<String, ReviewSummary> _summaries = {};
  bool _isLoading = false;

  // Getters
  List<ReviewModel> get reviews => _reviews;
  Map<String, ReviewSummary> get summaries => _summaries;
  bool get isLoading => _isLoading;

  // Load reviews for a target (product or user)
  Future<void> loadReviews(String targetId, ReviewType type) async {
    _isLoading = true;
    notifyListeners();

    try {
      // In real app, load from database
      _reviews = await _getMockReviews(targetId, type);
      _summaries[targetId] = ReviewSummary.fromReviews(_reviews);
    } catch (e) {
      debugPrint('Error loading reviews: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add new review
  Future<ReviewResult> addReview({
    required String targetId,
    required ReviewType type,
    required double rating,
    required String comment,
    List<String>? images,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return ReviewResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      // Check if user already reviewed this target
      final existingReview = _reviews.firstWhere(
        (review) => review.reviewerId == currentUser.id && review.targetId == targetId,
        orElse: () => ReviewModel(
          id: '',
          reviewerId: '',
          reviewerName: '',
          targetId: '',
          type: type,
          rating: 0,
          comment: '',
          createdAt: DateTime.now(),
        ),
      );

      if (existingReview.id.isNotEmpty) {
        return ReviewResult.error('لقد قمت بتقييم هذا العنصر من قبل');
      }

      final review = ReviewModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        reviewerId: currentUser.id,
        reviewerName: currentUser.displayName,
        reviewerImage: currentUser.profileImage,
        targetId: targetId,
        type: type,
        rating: rating,
        comment: comment,
        images: images ?? [],
        createdAt: DateTime.now(),
      );

      // Add to list
      _reviews.insert(0, review);
      
      // Update summary
      _summaries[targetId] = ReviewSummary.fromReviews(
        _reviews.where((r) => r.targetId == targetId).toList(),
      );

      notifyListeners();

      return ReviewResult.success('تم إضافة التقييم بنجاح', review);
    } catch (e) {
      debugPrint('Error adding review: $e');
      return ReviewResult.error('حدث خطأ أثناء إضافة التقييم');
    }
  }

  // Update review
  Future<ReviewResult> updateReview({
    required String reviewId,
    double? rating,
    String? comment,
    List<String>? images,
  }) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return ReviewResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex == -1) {
        return ReviewResult.error('التقييم غير موجود');
      }

      final existingReview = _reviews[reviewIndex];
      if (existingReview.reviewerId != currentUser.id) {
        return ReviewResult.error('ليس لديك صلاحية لتعديل هذا التقييم');
      }

      final updatedReview = existingReview.copyWith(
        rating: rating,
        comment: comment,
        images: images,
        updatedAt: DateTime.now(),
      );

      _reviews[reviewIndex] = updatedReview;
      
      // Update summary
      _summaries[existingReview.targetId] = ReviewSummary.fromReviews(
        _reviews.where((r) => r.targetId == existingReview.targetId).toList(),
      );

      notifyListeners();

      return ReviewResult.success('تم تحديث التقييم بنجاح', updatedReview);
    } catch (e) {
      debugPrint('Error updating review: $e');
      return ReviewResult.error('حدث خطأ أثناء تحديث التقييم');
    }
  }

  // Delete review
  Future<ReviewResult> deleteReview(String reviewId) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return ReviewResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex == -1) {
        return ReviewResult.error('التقييم غير موجود');
      }

      final review = _reviews[reviewIndex];
      if (review.reviewerId != currentUser.id) {
        return ReviewResult.error('ليس لديك صلاحية لحذف هذا التقييم');
      }

      _reviews.removeAt(reviewIndex);
      
      // Update summary
      _summaries[review.targetId] = ReviewSummary.fromReviews(
        _reviews.where((r) => r.targetId == review.targetId).toList(),
      );

      notifyListeners();

      return ReviewResult.success('تم حذف التقييم بنجاح');
    } catch (e) {
      debugPrint('Error deleting review: $e');
      return ReviewResult.error('حدث خطأ أثناء حذف التقييم');
    }
  }

  // Mark review as helpful
  Future<bool> markReviewHelpful(String reviewId) async {
    try {
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex != -1) {
        _reviews[reviewIndex] = _reviews[reviewIndex].copyWith(
          helpfulCount: _reviews[reviewIndex].helpfulCount + 1,
        );
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error marking review as helpful: $e');
      return false;
    }
  }

  // Report review
  Future<bool> reportReview(String reviewId, String reason) async {
    try {
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex != -1) {
        _reviews[reviewIndex] = _reviews[reviewIndex].copyWith(
          reportCount: _reviews[reviewIndex].reportCount + 1,
        );
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error reporting review: $e');
      return false;
    }
  }

  // Get review summary for target
  ReviewSummary? getReviewSummary(String targetId) {
    return _summaries[targetId];
  }

  // Get reviews by type
  List<ReviewModel> getReviewsByType(ReviewType type) {
    return _reviews.where((review) => review.type == type).toList();
  }

  // Get user's reviews
  List<ReviewModel> getUserReviews(String userId) {
    return _reviews.where((review) => review.reviewerId == userId).toList();
  }

  // Check if user can review target
  bool canUserReview(String targetId) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    // Check if user already reviewed
    return !_reviews.any(
      (review) => review.reviewerId == currentUser.id && review.targetId == targetId,
    );
  }

  // Mock data
  Future<List<ReviewModel>> _getMockReviews(String targetId, ReviewType type) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final currentUser = _authService.currentUser;
    if (currentUser == null) return [];

    return [
      ReviewModel(
        id: '1',
        reviewerId: 'user1',
        reviewerName: 'أحمد محمد',
        targetId: targetId,
        type: type,
        rating: 5.0,
        comment: 'منتج ممتاز وجودة عالية، أنصح بالشراء',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        isVerified: true,
        helpfulCount: 12,
      ),
      ReviewModel(
        id: '2',
        reviewerId: 'user2',
        reviewerName: 'فاطمة علي',
        targetId: targetId,
        type: type,
        rating: 4.0,
        comment: 'جيد جداً ولكن التوصيل كان متأخر قليلاً',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        helpfulCount: 8,
      ),
      ReviewModel(
        id: '3',
        reviewerId: 'user3',
        reviewerName: 'محمد السعيد',
        targetId: targetId,
        type: type,
        rating: 3.5,
        comment: 'مقبول بشكل عام، السعر مناسب',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        helpfulCount: 3,
      ),
    ];
  }

  // Clear cache
  void clearCache() {
    _reviews.clear();
    _summaries.clear();
    notifyListeners();
  }
}

class ReviewResult {
  final bool isSuccess;
  final String message;
  final ReviewModel? review;

  ReviewResult._(this.isSuccess, this.message, [this.review]);

  factory ReviewResult.success(String message, [ReviewModel? review]) {
    return ReviewResult._(true, message, review);
  }

  factory ReviewResult.error(String message) {
    return ReviewResult._(false, message);
  }
}
