[{"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/color/common_google_signin_btn_text_dark.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-16:/color/common_google_signin_btn_text_dark.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/color/common_google_signin_btn_tint.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-16:/color/common_google_signin_btn_tint.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/color/common_google_signin_btn_text_light.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-16:/color/common_google_signin_btn_text_light.xml"}]