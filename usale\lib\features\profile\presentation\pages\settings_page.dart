import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../services/language_service.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _notificationsEnabled = true;
  bool _emailNotifications = true;
  bool _smsNotifications = false;
  bool _pushNotifications = true;
  bool _darkModeEnabled = false;
  bool _locationEnabled = true;
  bool _chatEnabled = true;
  String _selectedCurrency = 'ريال سعودي (SAR)';

  final List<String> _currencies = [
    'ريال سعودي (SAR)',
    'درهم إماراتي (AED)',
    'دولار أمريكي (USD)',
    'يورو (EUR)',
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final languageService = Provider.of<LanguageService>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(localizations.settings),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: ListView(
        children: [
          // Notifications Section
          _buildSection(
            'الإشعارات',
            [
              _buildSwitchTile(
                'تفعيل الإشعارات',
                'استقبال جميع الإشعارات',
                Icons.notifications_outlined,
                _notificationsEnabled,
                (value) {
                  setState(() {
                    _notificationsEnabled = value;
                  });
                },
              ),
              if (_notificationsEnabled) ...[
                _buildSwitchTile(
                  'إشعارات البريد الإلكتروني',
                  'استقبال الإشعارات عبر البريد الإلكتروني',
                  Icons.email_outlined,
                  _emailNotifications,
                  (value) {
                    setState(() {
                      _emailNotifications = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  'الرسائل النصية',
                  'استقبال الإشعارات عبر الرسائل النصية',
                  Icons.sms_outlined,
                  _smsNotifications,
                  (value) {
                    setState(() {
                      _smsNotifications = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  'الإشعارات الفورية',
                  'استقبال الإشعارات الفورية على الجهاز',
                  Icons.phone_android,
                  _pushNotifications,
                  (value) {
                    setState(() {
                      _pushNotifications = value;
                    });
                  },
                ),
              ],
            ],
          ),

          // Appearance Section
          _buildSection(
            localizations.language,
            [
              _buildSwitchTile(
                localizations.darkMode,
                'تفعيل الوضع الليلي للتطبيق',
                Icons.dark_mode_outlined,
                _darkModeEnabled,
                (value) {
                  setState(() {
                    _darkModeEnabled = value;
                  });
                },
              ),
              _buildLanguageTile(
                context,
                localizations,
                languageService,
              ),
              _buildDropdownTile(
                'العملة',
                'اختيار العملة المفضلة',
                Icons.attach_money_outlined,
                _selectedCurrency,
                _currencies,
                (value) {
                  setState(() {
                    _selectedCurrency = value!;
                  });
                },
              ),
            ],
          ),

          // Privacy Section
          _buildSection(
            'الخصوصية والأمان',
            [
              _buildSwitchTile(
                'مشاركة الموقع',
                'السماح للتطبيق بالوصول للموقع',
                Icons.location_on_outlined,
                _locationEnabled,
                (value) {
                  setState(() {
                    _locationEnabled = value;
                  });
                },
              ),
              _buildSwitchTile(
                'تفعيل الدردشة',
                'السماح للمستخدمين بإرسال رسائل',
                Icons.chat_bubble_outline,
                _chatEnabled,
                (value) {
                  setState(() {
                    _chatEnabled = value;
                  });
                },
              ),
              _buildActionTile(
                'تغيير كلمة المرور',
                'تحديث كلمة المرور الخاصة بك',
                Icons.lock_outline,
                () {
                  _showChangePasswordDialog();
                },
              ),
              _buildActionTile(
                'المستخدمون المحظورون',
                'إدارة قائمة المستخدمين المحظورين',
                Icons.block_outlined,
                () {
                  // Navigate to blocked users
                },
              ),
            ],
          ),

          // Account Section
          _buildSection(
            'الحساب',
            [
              _buildActionTile(
                'تصدير البيانات',
                'تحميل نسخة من بياناتك',
                Icons.download_outlined,
                () {
                  _showExportDataDialog();
                },
              ),
              _buildActionTile(
                'حذف الحساب',
                'حذف الحساب نهائياً',
                Icons.delete_outline,
                () {
                  _showDeleteAccountDialog();
                },
                isDestructive: true,
              ),
            ],
          ),

          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(
            AppConstants.defaultPadding,
            24,
            AppConstants.defaultPadding,
            12,
          ),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      secondary: Icon(icon, color: AppColors.textSecondary),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primary,
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppColors.textSecondary),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        underline: const SizedBox(),
        items: items.map((item) {
          return DropdownMenuItem(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildLanguageTile(
    BuildContext context,
    AppLocalizations localizations,
    LanguageService languageService,
  ) {
    return ListTile(
      leading: const Icon(Icons.language_outlined, color: AppColors.textSecondary),
      title: Text(localizations.language),
      subtitle: Text('اختيار لغة التطبيق'),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            languageService.currentLanguageName,
            style: const TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.chevron_right, color: AppColors.textLight),
        ],
      ),
      onTap: () => _showLanguageDialog(context, localizations, languageService),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppColors.error : AppColors.textSecondary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? AppColors.error : null,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right, color: AppColors.textLight),
      onTap: onTap,
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'كلمة المرور الحالية',
                prefixIcon: Icon(Icons.lock_outline),
              ),
              obscureText: true,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                prefixIcon: Icon(Icons.lock_outline),
              ),
              obscureText: true,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                prefixIcon: Icon(Icons.lock_outline),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تغيير كلمة المرور بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  void _showExportDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: const Text(
          'سيتم إرسال نسخة من بياناتك إلى بريدك الإلكتروني خلال 24 ساعة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم طلب تصدير البيانات بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(
    BuildContext context,
    AppLocalizations localizations,
    LanguageService languageService,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.language),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text(localizations.arabic),
              value: 'ar',
              groupValue: languageService.currentLanguageCode,
              onChanged: (value) async {
                if (value != null) {
                  Navigator.pop(context);
                  await languageService.changeLanguage(value);
                }
              },
              activeColor: AppColors.primary,
            ),
            RadioListTile<String>(
              title: Text(localizations.english),
              value: 'en',
              groupValue: languageService.currentLanguageCode,
              onChanged: (value) async {
                if (value != null) {
                  Navigator.pop(context);
                  await languageService.changeLanguage(value);
                }
              },
              activeColor: AppColors.primary,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text(
          'هل أنت متأكد من حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه وستفقد جميع بياناتك.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // In real app, implement account deletion
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف الحساب'),
          ),
        ],
      ),
    );
  }
}
