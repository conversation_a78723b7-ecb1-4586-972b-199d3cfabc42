import 'package:flutter/material.dart';
// import 'package:flutter/services.dart'; // Removed unnecessary import
import 'dart:async';

/// خدمة إدارة السمات الذكية
class SmartThemeService extends ChangeNotifier {
  static final SmartThemeService _instance = SmartThemeService._internal();
  factory SmartThemeService() => _instance;
  SmartThemeService._internal();

  // حالة السمة الحالية
  ThemeMode _themeMode = ThemeMode.system;
  bool _isAutoDarkMode = true;
  bool _isScheduledMode = false;
  TimeOfDay _darkModeStartTime = const TimeOfDay(hour: 20, minute: 0);
  TimeOfDay _darkModeEndTime = const TimeOfDay(hour: 7, minute: 0);
  
  // مؤقت للتحقق من الوقت
  Timer? _timeCheckTimer;
  
  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get isAutoDarkMode => _isAutoDarkMode;
  bool get isScheduledMode => _isScheduledMode;
  TimeOfDay get darkModeStartTime => _darkModeStartTime;
  TimeOfDay get darkModeEndTime => _darkModeEndTime;
  bool get isDarkMode => _themeMode == ThemeMode.dark || 
    (_themeMode == ThemeMode.system && _isSystemDarkMode());

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadSettings();
    _startTimeChecker();
    _updateThemeBasedOnSettings();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    // هنا يمكن تحميل الإعدادات من SharedPreferences
    // للبساطة، سنستخدم القيم الافتراضية
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    // هنا يمكن حفظ الإعدادات في SharedPreferences
  }

  /// تحديث السمة بناءً على الإعدادات
  void _updateThemeBasedOnSettings() {
    if (_isAutoDarkMode) {
      _themeMode = ThemeMode.system;
    } else if (_isScheduledMode) {
      _themeMode = _shouldUseDarkModeBasedOnTime() ? ThemeMode.dark : ThemeMode.light;
    }
    notifyListeners();
  }

  /// التحقق من إعدادات النظام
  bool _isSystemDarkMode() {
    final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    return brightness == Brightness.dark;
  }

  /// التحقق من الوقت المجدول
  bool _shouldUseDarkModeBasedOnTime() {
    final now = TimeOfDay.now();
    final nowMinutes = now.hour * 60 + now.minute;
    final startMinutes = _darkModeStartTime.hour * 60 + _darkModeStartTime.minute;
    final endMinutes = _darkModeEndTime.hour * 60 + _darkModeEndTime.minute;

    if (startMinutes < endMinutes) {
      // نفس اليوم
      return nowMinutes >= startMinutes && nowMinutes < endMinutes;
    } else {
      // عبر منتصف الليل
      return nowMinutes >= startMinutes || nowMinutes < endMinutes;
    }
  }

  /// بدء مؤقت التحقق من الوقت
  void _startTimeChecker() {
    _timeCheckTimer?.cancel();
    _timeCheckTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_isScheduledMode) {
        _updateThemeBasedOnSettings();
      }
    });
  }

  /// تفعيل/إلغاء تفعيل الوضع التلقائي
  void toggleAutoDarkMode(bool enabled) {
    _isAutoDarkMode = enabled;
    if (enabled) {
      _isScheduledMode = false;
      _themeMode = ThemeMode.system;
    }
    _saveSettings();
    notifyListeners();
  }

  /// تفعيل/إلغاء تفعيل الوضع المجدول
  void toggleScheduledMode(bool enabled) {
    _isScheduledMode = enabled;
    if (enabled) {
      _isAutoDarkMode = false;
      _updateThemeBasedOnSettings();
    }
    _saveSettings();
    notifyListeners();
  }

  /// تحديد وقت بداية الوضع الليلي
  void setDarkModeStartTime(TimeOfDay time) {
    _darkModeStartTime = time;
    if (_isScheduledMode) {
      _updateThemeBasedOnSettings();
    }
    _saveSettings();
    notifyListeners();
  }

  /// تحديد وقت نهاية الوضع الليلي
  void setDarkModeEndTime(TimeOfDay time) {
    _darkModeEndTime = time;
    if (_isScheduledMode) {
      _updateThemeBasedOnSettings();
    }
    _saveSettings();
    notifyListeners();
  }

  /// تغيير السمة يدوياً
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _isAutoDarkMode = mode == ThemeMode.system;
    _isScheduledMode = false;
    _saveSettings();
    notifyListeners();
  }

  /// تبديل السمة
  void toggleTheme() {
    if (_themeMode == ThemeMode.light) {
      setThemeMode(ThemeMode.dark);
    } else if (_themeMode == ThemeMode.dark) {
      setThemeMode(ThemeMode.light);
    } else {
      setThemeMode(_isSystemDarkMode() ? ThemeMode.light : ThemeMode.dark);
    }
  }

  /// الحصول على لون تكيفي حسب السمة
  Color getAdaptiveColor({
    required Color lightColor,
    required Color darkColor,
  }) {
    return isDarkMode ? darkColor : lightColor;
  }

  /// الحصول على أيقونة تكيفية حسب السمة
  IconData getAdaptiveIcon({
    required IconData lightIcon,
    required IconData darkIcon,
  }) {
    return isDarkMode ? darkIcon : lightIcon;
  }

  /// تطبيق تأثير الاهتزاز عند تغيير السمة
  // void _applyHapticFeedback() { // Removed unused method
  //   HapticFeedback.lightImpact();
  // }

  /// تنظيف الموارد
  @override
  void dispose() {
    _timeCheckTimer?.cancel();
    super.dispose();
  }
}

/// ألوان السمة الذكية
class SmartColors {
  static const Color primaryLight = Color(0xFF2E7D32);
  static const Color primaryDark = Color(0xFF4CAF50);
  
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  
  static const Color textPrimaryLight = Color(0xFF212121);
  static const Color textPrimaryDark = Color(0xFFE0E0E0);
  
  static const Color textSecondaryLight = Color(0xFF757575);
  static const Color textSecondaryDark = Color(0xFFBDBDBD);
  
  static const Color cardLight = Color(0xFFFFFFFF);
  static const Color cardDark = Color(0xFF2C2C2C);
  
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF404040);
}

/// سمات التطبيق الذكية
class SmartThemes {
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primarySwatch: Colors.teal,
    primaryColor: SmartColors.primaryLight,
    scaffoldBackgroundColor: SmartColors.backgroundLight,
    cardColor: SmartColors.cardLight,
    appBarTheme: const AppBarTheme(
      backgroundColor: SmartColors.primaryLight,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: SmartColors.textPrimaryLight),
      bodyMedium: TextStyle(color: SmartColors.textSecondaryLight),
    ),
    cardTheme: CardThemeData(
      color: SmartColors.cardLight,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: SmartColors.borderLight),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: SmartColors.borderLight),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: SmartColors.primaryLight, width: 2),
      ),
    ),
  );

  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primarySwatch: Colors.teal,
    primaryColor: SmartColors.primaryDark,
    scaffoldBackgroundColor: SmartColors.backgroundDark,
    cardColor: SmartColors.cardDark,
    appBarTheme: const AppBarTheme(
      backgroundColor: SmartColors.primaryDark,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: SmartColors.textPrimaryDark),
      bodyMedium: TextStyle(color: SmartColors.textSecondaryDark),
    ),
    cardTheme: CardThemeData(
      color: SmartColors.cardDark,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: SmartColors.borderDark),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: SmartColors.borderDark),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: SmartColors.primaryDark, width: 2),
      ),
    ),
  );
}
