import 'dart:io';
// import 'dart:typed_data'; // Removed unnecessary import
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

class FirebaseStorageService {
  // Singleton pattern
  static final FirebaseStorageService _instance = FirebaseStorageService._internal();
  factory FirebaseStorageService() => _instance;
  FirebaseStorageService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Upload user profile image
  Future<String?> uploadProfileImage(String userId, XFile imageFile) async {
    try {
      final fileName = 'profile_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final ref = _storage.ref().child('users/$userId/profile/$fileName');

      UploadTask uploadTask;
      if (kIsWeb) {
        final bytes = await imageFile.readAsBytes();
        uploadTask = ref.putData(
          bytes,
          SettableMetadata(contentType: 'image/jpeg'),
        );
      } else {
        uploadTask = ref.putFile(File(imageFile.path));
      }

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading profile image: $e');
      return null;
    }
  }

  // Upload product images
  Future<List<String>> uploadProductImages(String productId, List<XFile> imageFiles) async {
    final List<String> downloadUrls = [];

    try {
      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];
        final fileName = 'image_${i}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final ref = _storage.ref().child('products/$productId/$fileName');

        UploadTask uploadTask;
        if (kIsWeb) {
          final bytes = await imageFile.readAsBytes();
          uploadTask = ref.putData(
            bytes,
            SettableMetadata(contentType: 'image/jpeg'),
          );
        } else {
          uploadTask = ref.putFile(File(imageFile.path));
        }

        final snapshot = await uploadTask;
        final downloadUrl = await snapshot.ref.getDownloadURL();
        downloadUrls.add(downloadUrl);
      }

      return downloadUrls;
    } catch (e) {
      debugPrint('Error uploading product images: $e');
      return downloadUrls; // Return partial results
    }
  }

  // Upload chat attachment
  Future<String?> uploadChatAttachment(String chatId, XFile file) async {
    try {
      final fileName = 'attachment_${DateTime.now().millisecondsSinceEpoch}_${file.name}';
      final ref = _storage.ref().child('chats/$chatId/attachments/$fileName');

      UploadTask uploadTask;
      if (kIsWeb) {
        final bytes = await file.readAsBytes();
        uploadTask = ref.putData(
          bytes,
          SettableMetadata(contentType: file.mimeType),
        );
      } else {
        uploadTask = ref.putFile(File(file.path));
      }

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading chat attachment: $e');
      return null;
    }
  }

  // Upload review images
  Future<List<String>> uploadReviewImages(String reviewId, List<XFile> imageFiles) async {
    final List<String> downloadUrls = [];

    try {
      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];
        final fileName = 'review_image_${i}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final ref = _storage.ref().child('reviews/$reviewId/images/$fileName');

        UploadTask uploadTask;
        if (kIsWeb) {
          final bytes = await imageFile.readAsBytes();
          uploadTask = ref.putData(
            bytes,
            SettableMetadata(contentType: 'image/jpeg'),
          );
        } else {
          uploadTask = ref.putFile(File(imageFile.path));
        }

        final snapshot = await uploadTask;
        final downloadUrl = await snapshot.ref.getDownloadURL();
        downloadUrls.add(downloadUrl);
      }

      return downloadUrls;
    } catch (e) {
      debugPrint('Error uploading review images: $e');
      return downloadUrls; // Return partial results
    }
  }

  // Upload file with progress tracking
  Future<String?> uploadFileWithProgress(
    String path,
    XFile file,
    Function(double)? onProgress,
  ) async {
    try {
      final ref = _storage.ref().child(path);

      UploadTask uploadTask;
      if (kIsWeb) {
        final bytes = await file.readAsBytes();
        uploadTask = ref.putData(
          bytes,
          SettableMetadata(contentType: file.mimeType),
        );
      } else {
        uploadTask = ref.putFile(File(file.path));
      }

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading file with progress: $e');
      return null;
    }
  }

  // Delete file
  Future<bool> deleteFile(String downloadUrl) async {
    try {
      final ref = _storage.refFromURL(downloadUrl);
      await ref.delete();
      return true;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  // Delete multiple files
  Future<bool> deleteFiles(List<String> downloadUrls) async {
    try {
      for (final url in downloadUrls) {
        final ref = _storage.refFromURL(url);
        await ref.delete();
      }
      return true;
    } catch (e) {
      debugPrint('Error deleting files: $e');
      return false;
    }
  }

  // Get file metadata
  Future<FullMetadata?> getFileMetadata(String downloadUrl) async {
    try {
      final ref = _storage.refFromURL(downloadUrl);
      return await ref.getMetadata();
    } catch (e) {
      debugPrint('Error getting file metadata: $e');
      return null;
    }
  }

  // Compress and upload image
  Future<String?> compressAndUploadImage(
    String path,
    XFile imageFile, {
    int quality = 85,
    int maxWidth = 1920,
    int maxHeight = 1080,
  }) async {
    try {
      // In a real app, you would compress the image here
      // For now, just upload the original
      return await uploadFileWithProgress(path, imageFile, null);
    } catch (e) {
      debugPrint('Error compressing and uploading image: $e');
      return null;
    }
  }

  // Upload from bytes (useful for web)
  Future<String?> uploadFromBytes(
    String path,
    Uint8List bytes,
    String contentType,
  ) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putData(
        bytes,
        SettableMetadata(contentType: contentType),
      );

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading from bytes: $e');
      return null;
    }
  }

  // Get download URL from path
  Future<String?> getDownloadUrl(String path) async {
    try {
      final ref = _storage.ref().child(path);
      return await ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error getting download URL: $e');
      return null;
    }
  }

  // List files in a directory
  Future<List<Reference>> listFiles(String path) async {
    try {
      final ref = _storage.ref().child(path);
      final result = await ref.listAll();
      return result.items;
    } catch (e) {
      debugPrint('Error listing files: $e');
      return [];
    }
  }

  // Clean up temporary files (call periodically)
  Future<void> cleanupTempFiles(String userId) async {
    try {
      final tempRef = _storage.ref().child('temp/$userId');
      final result = await tempRef.listAll();
      
      final now = DateTime.now();
      for (final item in result.items) {
        final metadata = await item.getMetadata();
        final createdAt = metadata.timeCreated;
        
        if (createdAt != null) {
          final age = now.difference(createdAt);
          if (age.inHours > 24) {
            await item.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up temp files: $e');
    }
  }

  // Get storage usage for user
  Future<int> getUserStorageUsage(String userId) async {
    try {
      int totalSize = 0;
      
      // Check profile images
      final profileRef = _storage.ref().child('users/$userId/profile');
      final profileResult = await profileRef.listAll();
      for (final item in profileResult.items) {
        final metadata = await item.getMetadata();
        totalSize += metadata.size ?? 0;
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('Error getting user storage usage: $e');
      return 0;
    }
  }
}
