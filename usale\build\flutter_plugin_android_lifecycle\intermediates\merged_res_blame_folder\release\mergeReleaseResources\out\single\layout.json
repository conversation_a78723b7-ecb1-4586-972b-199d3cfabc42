[{"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/layout/ime_base_split_test_activity.xml"}, {"merged": "io.flutter.plugins.flutter_plugin_android_lifecycle-release-25:/layout/custom_dialog.xml", "source": "io.flutter.plugins.flutter_plugin_android_lifecycle-core-1.13.1-10:/layout/custom_dialog.xml"}]