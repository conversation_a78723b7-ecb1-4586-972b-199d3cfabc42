{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Usale\\usale\\build\\.cxx\\RelWithDebInfo\\3j1f1q1r\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Usale\\usale\\build\\.cxx\\RelWithDebInfo\\3j1f1q1r\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}