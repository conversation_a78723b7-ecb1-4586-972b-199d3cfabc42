import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product_model.dart';
import '../core/constants/app_constants.dart';
import 'database_service.dart';
import 'auth_service.dart';

class FavoritesService {
  static const String _favoritesKey = AppConstants.favoritesKey;
  static const String _collectionsKey = 'favorite_collections';
  
  // Singleton pattern
  static final FavoritesService _instance = FavoritesService._internal();
  factory FavoritesService() => _instance;
  FavoritesService._internal();

  // Cache for favorites
  List<ProductModel> _favorites = [];
  Map<String, FavoriteCollection> _collections = {};
  bool _isLoaded = false;
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();

  // Load favorites from storage
  Future<void> loadFavorites() async {
    if (_isLoaded) return;
    
    final prefs = await SharedPreferences.getInstance();
    
    // Load favorites
    final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
    _favorites = favoritesJson
        .map((json) => ProductModel.fromJson(jsonDecode(json)))
        .toList();
    
    // Load collections
    final collectionsJson = prefs.getStringList(_collectionsKey) ?? [];
    _collections = {};
    for (final json in collectionsJson) {
      final collection = FavoriteCollection.fromJson(jsonDecode(json));
      _collections[collection.id] = collection;
    }
    
    _isLoaded = true;
  }

  // Save favorites to storage
  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Save favorites
    final favoritesJson = _favorites
        .map((product) => jsonEncode(product.toJson()))
        .toList();
    await prefs.setStringList(_favoritesKey, favoritesJson);
    
    // Save collections
    final collectionsJson = _collections.values
        .map((collection) => jsonEncode(collection.toJson()))
        .toList();
    await prefs.setStringList(_collectionsKey, collectionsJson);
  }

  // Get all favorites
  Future<List<ProductModel>> getFavorites() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return [];

    try {
      _favorites = await _databaseService.getFavoriteProducts(currentUser.id);
      return List.from(_favorites);
    } catch (e) {
      // Fallback to cached data
      await loadFavorites();
      return List.from(_favorites);
    }
  }

  // Check if product is favorite
  Future<bool> isFavorite(String productId) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    try {
      return await _databaseService.isFavorite(currentUser.id, productId);
    } catch (e) {
      // Fallback to cached data
      await loadFavorites();
      return _favorites.any((product) => product.id == productId);
    }
  }

  // Add product to favorites
  Future<bool> addToFavorites(ProductModel product) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    try {
      final result = await _databaseService.addToFavorites(currentUser.id, product.id);
      if (result > 0) {
        // Update cache
        if (!_favorites.any((p) => p.id == product.id)) {
          _favorites.add(product);
        }
        return true;
      }
      return false;
    } catch (e) {
      // Fallback to local storage
      await loadFavorites();
      if (_favorites.any((p) => p.id == product.id)) {
        return false; // Already in favorites
      }
      _favorites.add(product);
      await _saveFavorites();
      return true;
    }
  }

  // Remove product from favorites
  Future<bool> removeFromFavorites(String productId) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    try {
      final result = await _databaseService.removeFromFavorites(currentUser.id, productId);
      if (result > 0) {
        // Update cache
        _favorites.removeWhere((product) => product.id == productId);
        return true;
      }
      return false;
    } catch (e) {
      // Fallback to local storage
      await loadFavorites();
      final initialLength = _favorites.length;
      _favorites.removeWhere((product) => product.id == productId);

      if (_favorites.length < initialLength) {
        // Also remove from all collections
        for (final collection in _collections.values) {
          collection.productIds.remove(productId);
        }
        await _saveFavorites();
        return true;
      }
      return false;
    }
  }

  // Toggle favorite status
  Future<bool> toggleFavorite(ProductModel product) async {
    final isFav = await isFavorite(product.id);
    if (isFav) {
      await removeFromFavorites(product.id);
      return false;
    } else {
      await addToFavorites(product);
      return true;
    }
  }

  // Get favorites count
  Future<int> getFavoritesCount() async {
    await loadFavorites();
    return _favorites.length;
  }

  // Clear all favorites
  Future<void> clearFavorites() async {
    _favorites.clear();
    _collections.clear();
    await _saveFavorites();
  }

  // Collection management
  Future<List<FavoriteCollection>> getCollections() async {
    await loadFavorites();
    return _collections.values.toList();
  }

  Future<FavoriteCollection?> getCollection(String collectionId) async {
    await loadFavorites();
    return _collections[collectionId];
  }

  Future<FavoriteCollection> createCollection(String name, String description) async {
    await loadFavorites();
    
    final collection = FavoriteCollection(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      createdAt: DateTime.now(),
      productIds: [],
    );
    
    _collections[collection.id] = collection;
    await _saveFavorites();
    return collection;
  }

  Future<bool> deleteCollection(String collectionId) async {
    await loadFavorites();
    
    if (_collections.containsKey(collectionId)) {
      _collections.remove(collectionId);
      await _saveFavorites();
      return true;
    }
    
    return false;
  }

  Future<bool> addToCollection(String collectionId, String productId) async {
    await loadFavorites();
    
    final collection = _collections[collectionId];
    if (collection != null && !collection.productIds.contains(productId)) {
      collection.productIds.add(productId);
      await _saveFavorites();
      return true;
    }
    
    return false;
  }

  Future<bool> removeFromCollection(String collectionId, String productId) async {
    await loadFavorites();
    
    final collection = _collections[collectionId];
    if (collection != null && collection.productIds.contains(productId)) {
      collection.productIds.remove(productId);
      await _saveFavorites();
      return true;
    }
    
    return false;
  }

  Future<List<ProductModel>> getCollectionProducts(String collectionId) async {
    await loadFavorites();
    
    final collection = _collections[collectionId];
    if (collection == null) return [];
    
    return _favorites
        .where((product) => collection.productIds.contains(product.id))
        .toList();
  }

  // Search in favorites
  Future<List<ProductModel>> searchFavorites(String query) async {
    await loadFavorites();
    
    if (query.isEmpty) return _favorites;
    
    final lowerQuery = query.toLowerCase();
    return _favorites.where((product) {
      return product.title.toLowerCase().contains(lowerQuery) ||
             product.description.toLowerCase().contains(lowerQuery) ||
             product.category.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // Filter favorites
  Future<List<ProductModel>> filterFavorites({
    String? category,
    String? condition,
    double? minPrice,
    double? maxPrice,
  }) async {
    await loadFavorites();
    
    return _favorites.where((product) {
      if (category != null && product.category != category) return false;
      if (condition != null && product.condition != condition) return false;
      if (minPrice != null && product.price < minPrice) return false;
      if (maxPrice != null && product.price > maxPrice) return false;
      return true;
    }).toList();
  }

  // Get favorites by category
  Future<Map<String, List<ProductModel>>> getFavoritesByCategory() async {
    await loadFavorites();
    
    final Map<String, List<ProductModel>> categorized = {};
    
    for (final product in _favorites) {
      if (!categorized.containsKey(product.category)) {
        categorized[product.category] = [];
      }
      categorized[product.category]!.add(product);
    }
    
    return categorized;
  }

  // Get recently added favorites
  Future<List<ProductModel>> getRecentFavorites({int limit = 10}) async {
    await loadFavorites();
    
    final sorted = List<ProductModel>.from(_favorites);
    sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return sorted.take(limit).toList();
  }
}

class FavoriteCollection {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final List<String> productIds;

  FavoriteCollection({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.productIds,
  });

  factory FavoriteCollection.fromJson(Map<String, dynamic> json) {
    return FavoriteCollection(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      productIds: List<String>.from(json['product_ids'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'product_ids': productIds,
    };
  }

  FavoriteCollection copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    List<String>? productIds,
  }) {
    return FavoriteCollection(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      productIds: productIds ?? this.productIds,
    );
  }

  @override
  String toString() {
    return 'FavoriteCollection(id: $id, name: $name, productIds: ${productIds.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FavoriteCollection && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
