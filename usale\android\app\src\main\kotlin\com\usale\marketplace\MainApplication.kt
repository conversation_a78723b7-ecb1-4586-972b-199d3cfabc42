package com.usale.marketplace

import io.flutter.app.FlutterApplication
// Removed plugin registry imports for simplicity
import androidx.multidex.MultiDex
import android.content.Context

class MainApplication : FlutterApplication() {
    
    override fun onCreate() {
        super.onCreate()
    }
    
    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }
    
    // Removed registerWith method for simplicity
}
