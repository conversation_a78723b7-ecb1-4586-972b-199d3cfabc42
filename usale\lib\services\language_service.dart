import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'ar';
  
  // Singleton pattern
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  Locale _currentLocale = const Locale('ar', 'SA');
  
  Locale get currentLocale => _currentLocale;
  String get currentLanguageCode => _currentLocale.languageCode;
  bool get isArabic => _currentLocale.languageCode == 'ar';
  bool get isEnglish => _currentLocale.languageCode == 'en';

  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('ar', 'SA'), // Arabic (Saudi Arabia)
    Locale('en', 'US'), // English (United States)
  ];

  // Language names
  static const Map<String, String> languageNames = {
    'ar': 'العربية',
    'en': 'English',
  };

  // Initialize language service
  Future<void> initialize() async {
    await _loadSavedLanguage();
  }

  // Load saved language from SharedPreferences
  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey) ?? _defaultLanguage;
      
      // Set locale based on saved language
      switch (savedLanguage) {
        case 'ar':
          _currentLocale = const Locale('ar', 'SA');
          break;
        case 'en':
          _currentLocale = const Locale('en', 'US');
          break;
        default:
          _currentLocale = const Locale('ar', 'SA');
      }
      
      notifyListeners();
    } catch (e) {
      // If there's an error, use default language
      _currentLocale = const Locale('ar', 'SA');
    }
  }

  // Change language
  Future<void> changeLanguage(String languageCode) async {
    if (languageCode == _currentLocale.languageCode) return;
    
    try {
      // Update current locale
      switch (languageCode) {
        case 'ar':
          _currentLocale = const Locale('ar', 'SA');
          break;
        case 'en':
          _currentLocale = const Locale('en', 'US');
          break;
        default:
          return; // Invalid language code
      }
      
      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      
      // Notify listeners
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error changing language: $e');
    }
  }

  // Toggle between Arabic and English
  Future<void> toggleLanguage() async {
    final newLanguage = isArabic ? 'en' : 'ar';
    await changeLanguage(newLanguage);
  }

  // Get language name
  String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode;
  }

  // Get current language name
  String get currentLanguageName => getLanguageName(currentLanguageCode);

  // Get text direction based on current language
  TextDirection get textDirection {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }

  // Check if locale is supported
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }

  // Get locale from language code
  Locale getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return const Locale('ar', 'SA');
      case 'en':
        return const Locale('en', 'US');
      default:
        return const Locale('ar', 'SA');
    }
  }

  // Format numbers based on current locale
  String formatNumber(num number) {
    if (isArabic) {
      // Convert to Arabic-Indic numerals
      return number.toString().replaceAllMapped(
        RegExp(r'[0-9]'),
        (match) => _arabicNumerals[match.group(0)!]!,
      );
    } else {
      return number.toString();
    }
  }

  // Arabic-Indic numerals mapping
  static const Map<String, String> _arabicNumerals = {
    '0': '٠',
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
  };

  // Format currency based on current locale
  String formatCurrency(num amount, {String currency = 'SAR'}) {
    if (isArabic) {
      return '${formatNumber(amount)} ريال';
    } else {
      return '$amount $currency';
    }
  }

  // Format date based on current locale
  String formatDate(DateTime date) {
    if (isArabic) {
      // Arabic date format
      final months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      return '${formatNumber(date.day)} ${months[date.month - 1]} ${formatNumber(date.year)}';
    } else {
      // English date format
      final months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return '${date.day} ${months[date.month - 1]} ${date.year}';
    }
  }

  // Format time ago based on current locale
  String formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (isArabic) {
      if (difference.inDays > 0) {
        return 'منذ ${formatNumber(difference.inDays)} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
      } else if (difference.inHours > 0) {
        return 'منذ ${formatNumber(difference.inHours)} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
      } else if (difference.inMinutes > 0) {
        return 'منذ ${formatNumber(difference.inMinutes)} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
      } else {
        return 'الآن';
      }
    } else {
      if (difference.inDays > 0) {
        return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
      } else {
        return 'Now';
      }
    }
  }

  // Get appropriate font family for current language
  String get fontFamily {
    return isArabic ? 'Cairo' : 'Roboto';
  }

  // Reset to default language
  Future<void> resetToDefault() async {
    await changeLanguage(_defaultLanguage);
  }

  // Clear saved language preference
  Future<void> clearLanguagePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_languageKey);
      await _loadSavedLanguage();
    } catch (e) {
      debugPrint('Error clearing language preference: $e');
    }
  }
}
