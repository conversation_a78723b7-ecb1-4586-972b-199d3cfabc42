{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "787,895,1002,1118", "endColumns": "107,106,115,104", "endOffsets": "890,997,1113,1218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,1223", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,1319"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "787,895,1002,1118", "endColumns": "107,106,115,104", "endOffsets": "890,997,1113,1218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,1223", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,1319"}}]}]}