import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:firebase_core/firebase_core.dart'; // Disabled for web compatibility
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Firebase (disabled for web compatibility)
// import 'firebase_options.dart';

// Core
import 'core/theme/app_theme.dart';
import 'core/localization/app_localizations.dart';

// Services
import 'services/language_service.dart';
import 'services/auth_service.dart';
import 'services/products_service.dart';
import 'services/search_service.dart';
import 'services/chat_service.dart';
import 'services/reviews_service.dart';
import 'services/notifications_service.dart';
import 'services/payment_service.dart';
import 'services/location_service.dart';
import 'services/analytics_service.dart';
import 'services/security_service.dart';

// Features
import 'features/auth/presentation/pages/auth_wrapper.dart';

// Firebase services disabled for web compatibility
// import 'services/firebase_auth_service.dart';
// import 'services/firestore_service.dart';
// import 'services/firebase_storage_service.dart';
// import 'services/firebase_messaging_service.dart';

// Mock Firebase Services for web compatibility
class MockFirebaseAuthService extends ChangeNotifier {
  Future<void> initialize() async {}
}

class MockFirestoreService extends ChangeNotifier {
  Future<void> initialize() async {}
}

class MockFirebaseStorageService extends ChangeNotifier {
  Future<void> initialize() async {}
}

class MockFirebaseMessagingService extends ChangeNotifier {
  Future<void> initialize() async {}
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase (disabled for web compatibility)
    // await Firebase.initializeApp(
    //   options: DefaultFirebaseOptions.currentPlatform,
    // );
    debugPrint('✅ Firebase initialization skipped for web compatibility');
  } catch (e) {
    debugPrint('❌ Firebase initialization failed: $e');
    // Continue with app initialization even if Firebase fails
  }

  // Initialize services
  final languageService = LanguageService();
  await languageService.initialize();

  final authService = AuthService();
  await authService.initialize();

  final productsService = ProductsService();
  final searchService = SearchService();
  final chatService = ChatService();
  final reviewsService = ReviewsService();
  final notificationsService = NotificationsService();
  final paymentService = PaymentService();
  final locationService = LocationService();
  final analyticsService = AnalyticsService();
  final securityService = SecurityService();
  // Mock Firebase services for web compatibility
  final firebaseAuthService = MockFirebaseAuthService();
  final firestoreService = MockFirestoreService();
  final firebaseStorageService = MockFirebaseStorageService();
  final firebaseMessagingService = MockFirebaseMessagingService();

  // Initialize services
  await notificationsService.initialize();
  await paymentService.initialize();
  await locationService.initialize();
  await analyticsService.initialize();
  await securityService.initialize();

  // Initialize Firebase services
  try {
    await firebaseAuthService.initialize();
    debugPrint('✅ Firebase Auth Service initialized');
  } catch (e) {
    debugPrint('❌ Firebase Auth Service failed: $e');
  }

  try {
    await firebaseMessagingService.initialize();
    debugPrint('✅ Firebase Messaging Service initialized');
  } catch (e) {
    debugPrint('❌ Firebase Messaging Service failed: $e');
  }

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(USaleApp(
    languageService: languageService,
    authService: authService,
    productsService: productsService,
    searchService: searchService,
    chatService: chatService,
    reviewsService: reviewsService,
    notificationsService: notificationsService,
    paymentService: paymentService,
    locationService: locationService,
    analyticsService: analyticsService,
    securityService: securityService,
    firebaseAuthService: firebaseAuthService,
    firestoreService: firestoreService,
    firebaseStorageService: firebaseStorageService,
    firebaseMessagingService: firebaseMessagingService,
  ));
}

class USaleApp extends StatelessWidget {
  final LanguageService languageService;
  final AuthService authService;
  final ProductsService productsService;
  final SearchService searchService;
  final ChatService chatService;
  final ReviewsService reviewsService;
  final NotificationsService notificationsService;
  final PaymentService paymentService;
  final LocationService locationService;
  final AnalyticsService analyticsService;
  final SecurityService securityService;
  final MockFirebaseAuthService firebaseAuthService;
  final MockFirestoreService firestoreService;
  final MockFirebaseStorageService firebaseStorageService;
  final MockFirebaseMessagingService firebaseMessagingService;

  const USaleApp({
    super.key,
    required this.languageService,
    required this.authService,
    required this.productsService,
    required this.searchService,
    required this.chatService,
    required this.reviewsService,
    required this.notificationsService,
    required this.paymentService,
    required this.locationService,
    required this.analyticsService,
    required this.securityService,
    required this.firebaseAuthService,
    required this.firestoreService,
    required this.firebaseStorageService,
    required this.firebaseMessagingService,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: languageService),
        ChangeNotifierProvider.value(value: authService),
        ChangeNotifierProvider.value(value: productsService),
        ChangeNotifierProvider.value(value: searchService),
        ChangeNotifierProvider.value(value: chatService),
        ChangeNotifierProvider.value(value: reviewsService),
        ChangeNotifierProvider.value(value: notificationsService),
        ChangeNotifierProvider.value(value: paymentService),
        ChangeNotifierProvider.value(value: locationService),
        ChangeNotifierProvider.value(value: analyticsService),
        ChangeNotifierProvider.value(value: securityService),
        ChangeNotifierProvider.value(value: firebaseAuthService),
        Provider.value(value: firestoreService),
        Provider.value(value: firebaseStorageService),
        Provider.value(value: firebaseMessagingService),
      ],
      child: Consumer<LanguageService>(
        builder: (context, languageService, child) {
          return MaterialApp(
            title: 'يو سيل - USale',
            debugShowCheckedModeBanner: false,

            // Theme Configuration
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,

            // Localization
            locale: languageService.currentLocale,
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: AppLocalizations.supportedLocales,

            // Home Page
            home: const AuthWrapper(),

            // Builder for custom configurations
            builder: (context, child) {
              return Directionality(
                textDirection: languageService.textDirection,
                child: MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                    textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                  ),
                  child: child!,
                ),
              );
            },
          );
        },
      ),
    );
  }
}
