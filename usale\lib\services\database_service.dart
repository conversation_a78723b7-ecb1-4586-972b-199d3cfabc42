import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user_model.dart';
import '../models/product_model.dart';

class DatabaseService {
  static const String _databaseName = 'usale_database.db';
  static const int _databaseVersion = 1;
  
  // Singleton pattern
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Database? _database;

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  // Create tables
  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT,
        profile_image TEXT,
        bio TEXT,
        location TEXT,
        address TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        last_login_at TEXT,
        is_verified INTEGER DEFAULT 0,
        user_type TEXT DEFAULT 'buyer',
        status TEXT DEFAULT 'active',
        rating REAL DEFAULT 0.0,
        reviews_count INTEGER DEFAULT 0,
        total_sales INTEGER DEFAULT 0,
        total_purchases INTEGER DEFAULT 0,
        favorite_categories TEXT,
        settings TEXT
      )
    ''');

    // Products table
    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        price REAL NOT NULL,
        category TEXT NOT NULL,
        condition TEXT NOT NULL,
        location TEXT NOT NULL,
        seller_id TEXT NOT NULL,
        seller_name TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_featured INTEGER DEFAULT 0,
        is_sold INTEGER DEFAULT 0,
        views_count INTEGER DEFAULT 0,
        likes_count INTEGER DEFAULT 0,
        images TEXT,
        tags TEXT,
        FOREIGN KEY (seller_id) REFERENCES users (id)
      )
    ''');

    // Favorites table
    await db.execute('''
      CREATE TABLE favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (product_id) REFERENCES products (id),
        UNIQUE(user_id, product_id)
      )
    ''');

    // Chats table
    await db.execute('''
      CREATE TABLE chats (
        id TEXT PRIMARY KEY,
        participant1_id TEXT NOT NULL,
        participant2_id TEXT NOT NULL,
        product_id TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        last_message TEXT,
        last_message_at TEXT,
        FOREIGN KEY (participant1_id) REFERENCES users (id),
        FOREIGN KEY (participant2_id) REFERENCES users (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    ''');

    // Messages table
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        chat_id TEXT NOT NULL,
        sender_id TEXT NOT NULL,
        content TEXT NOT NULL,
        message_type TEXT DEFAULT 'text',
        created_at TEXT NOT NULL,
        is_read INTEGER DEFAULT 0,
        attachment_url TEXT,
        FOREIGN KEY (chat_id) REFERENCES chats (id),
        FOREIGN KEY (sender_id) REFERENCES users (id)
      )
    ''');

    // Notifications table
    await db.execute('''
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT NOT NULL,
        body TEXT NOT NULL,
        type TEXT NOT NULL,
        data TEXT,
        created_at TEXT NOT NULL,
        is_read INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Search history table
    await db.execute('''
      CREATE TABLE search_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        query TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_products_seller_id ON products(seller_id)');
    await db.execute('CREATE INDEX idx_products_category ON products(category)');
    await db.execute('CREATE INDEX idx_products_created_at ON products(created_at)');
    await db.execute('CREATE INDEX idx_messages_chat_id ON messages(chat_id)');
    await db.execute('CREATE INDEX idx_notifications_user_id ON notifications(user_id)');
  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema changes here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // User operations
  Future<int> insertUser(UserModel user) async {
    final db = await database;
    return await db.insert(
      'users',
      _userToMap(user),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserModel?> getUser(String id) async {
    final db = await database;
    final maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isNotEmpty) {
      return _mapToUser(maps.first);
    }
    return null;
  }

  Future<UserModel?> getUserByEmail(String email) async {
    final db = await database;
    final maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );
    
    if (maps.isNotEmpty) {
      return _mapToUser(maps.first);
    }
    return null;
  }

  Future<int> updateUser(UserModel user) async {
    final db = await database;
    return await db.update(
      'users',
      _userToMap(user),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(String id) async {
    final db = await database;
    return await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Product operations
  Future<int> insertProduct(ProductModel product) async {
    final db = await database;
    return await db.insert(
      'products',
      _productToMap(product),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<ProductModel>> getProducts({
    String? category,
    String? sellerId,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    String whereClause = '';
    List<dynamic> whereArgs = [];
    
    if (category != null) {
      whereClause += 'category = ?';
      whereArgs.add(category);
    }
    
    if (sellerId != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'seller_id = ?';
      whereArgs.add(sellerId);
    }
    
    final maps = await db.query(
      'products',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
    
    return maps.map((map) => _mapToProduct(map)).toList();
  }

  Future<ProductModel?> getProduct(String id) async {
    final db = await database;
    final maps = await db.query(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isNotEmpty) {
      return _mapToProduct(maps.first);
    }
    return null;
  }

  Future<int> updateProduct(ProductModel product) async {
    final db = await database;
    return await db.update(
      'products',
      _productToMap(product),
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  Future<int> deleteProduct(String id) async {
    final db = await database;
    return await db.delete(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Search products
  Future<List<ProductModel>> searchProducts(String query) async {
    final db = await database;
    final maps = await db.query(
      'products',
      where: 'title LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    
    return maps.map((map) => _mapToProduct(map)).toList();
  }

  // Favorites operations
  Future<int> addToFavorites(String userId, String productId) async {
    final db = await database;
    return await db.insert(
      'favorites',
      {
        'user_id': userId,
        'product_id': productId,
        'created_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
  }

  Future<int> removeFromFavorites(String userId, String productId) async {
    final db = await database;
    return await db.delete(
      'favorites',
      where: 'user_id = ? AND product_id = ?',
      whereArgs: [userId, productId],
    );
  }

  Future<List<ProductModel>> getFavoriteProducts(String userId) async {
    final db = await database;
    final maps = await db.rawQuery('''
      SELECT p.* FROM products p
      INNER JOIN favorites f ON p.id = f.product_id
      WHERE f.user_id = ?
      ORDER BY f.created_at DESC
    ''', [userId]);
    
    return maps.map((map) => _mapToProduct(map)).toList();
  }

  Future<bool> isFavorite(String userId, String productId) async {
    final db = await database;
    final maps = await db.query(
      'favorites',
      where: 'user_id = ? AND product_id = ?',
      whereArgs: [userId, productId],
    );
    
    return maps.isNotEmpty;
  }

  // Helper methods for data conversion
  Map<String, dynamic> _userToMap(UserModel user) {
    final map = user.toJson();
    map['favorite_categories'] = jsonEncode(user.favoriteCategories);
    map['settings'] = jsonEncode(user.settings.toJson());
    map['is_verified'] = user.isVerified ? 1 : 0;
    return map;
  }

  UserModel _mapToUser(Map<String, dynamic> map) {
    final favoriteCategories = map['favorite_categories'] != null
        ? List<String>.from(jsonDecode(map['favorite_categories']))
        : <String>[];
    
    final settings = map['settings'] != null
        ? UserSettings.fromJson(jsonDecode(map['settings']))
        : UserSettings();

    return UserModel(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      phone: map['phone'],
      profileImage: map['profile_image'],
      bio: map['bio'],
      location: map['location'],
      address: map['address'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      lastLoginAt: map['last_login_at'] != null ? DateTime.parse(map['last_login_at']) : null,
      isVerified: map['is_verified'] == 1,
      userType: UserType.values.firstWhere(
        (e) => e.toString() == 'UserType.${map['user_type']}',
        orElse: () => UserType.buyer,
      ),
      status: AccountStatus.values.firstWhere(
        (e) => e.toString() == 'AccountStatus.${map['status']}',
        orElse: () => AccountStatus.active,
      ),
      rating: map['rating']?.toDouble() ?? 0.0,
      reviewsCount: map['reviews_count'] ?? 0,
      totalSales: map['total_sales'] ?? 0,
      totalPurchases: map['total_purchases'] ?? 0,
      favoriteCategories: favoriteCategories,
      settings: settings,
    );
  }

  Map<String, dynamic> _productToMap(ProductModel product) {
    final map = product.toJson();
    map['is_featured'] = product.isFeatured ? 1 : 0;
    map['is_sold'] = product.status == ProductStatus.sold ? 1 : 0;
    return map;
  }

  ProductModel _mapToProduct(Map<String, dynamic> map) {
    return ProductModel(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      price: map['price']?.toDouble() ?? 0.0,
      category: map['category'],
      condition: map['condition'],
      location: map['location'],
      sellerId: map['seller_id'],
      sellerName: map['seller_name'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isFeatured: map['is_featured'] == 1,
      status: map['is_sold'] == 1 ? ProductStatus.sold : ProductStatus.available,
      viewsCount: map['views_count'] ?? 0,
      favoritesCount: map['likes_count'] ?? 0,
      images: map['images'] != null ? List<String>.from(jsonDecode(map['images'])) : [],
      tags: map['tags'] != null ? List<String>.from(jsonDecode(map['tags'])) : [],
    );
  }

  // Close database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  // Clear all data (for testing or reset)
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('users');
    await db.delete('products');
    await db.delete('favorites');
    await db.delete('chats');
    await db.delete('messages');
    await db.delete('notifications');
    await db.delete('search_history');
  }
}
