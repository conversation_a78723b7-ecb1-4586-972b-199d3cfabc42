{"logs": [{"outputFile": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a75f01a7707dc356bd2b3ef3fe83d0a8\\transformed\\jetified-play-services-base-18.0.1\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,878,1033,1169,1274,1421,1551,1678,1931,2103,2210,2367,2501,2646,2813,2875,2939", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "873,1028,1164,1269,1416,1546,1673,1779,2098,2205,2362,2496,2641,2808,2870,2934,3014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b763d777ce28d1ba4f435305e05f97a0\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1784", "endColumns": "146", "endOffsets": "1926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,3019", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,3115"}}]}, {"outputFile": "io.flutter.plugins.googlemaps.google_maps_flutter_android-mergeReleaseResources-28:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a75f01a7707dc356bd2b3ef3fe83d0a8\\transformed\\jetified-play-services-base-18.0.1\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,878,1033,1169,1274,1421,1551,1678,1931,2103,2210,2367,2501,2646,2813,2875,2939", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "873,1028,1164,1269,1416,1546,1673,1779,2098,2205,2362,2496,2641,2808,2870,2934,3014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b763d777ce28d1ba4f435305e05f97a0\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1784", "endColumns": "146", "endOffsets": "1926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,3019", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,3115"}}]}]}