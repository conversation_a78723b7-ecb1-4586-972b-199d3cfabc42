import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/notification_model.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<NotificationModel> _allNotifications = [];
  List<NotificationModel> _unreadNotifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadNotifications() {
    // Simulate loading notifications
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _allNotifications = [
          NotificationModel(
            id: '1',
            userId: 'user1',
            title: 'رسالة جديدة',
            body: 'أحمد محمد أرسل لك رسالة حول آيفون 15 برو',
            type: NotificationType.message,
            createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
            isRead: false,
            data: {'chatId': '1', 'productId': 'product1'},
          ),
          NotificationModel(
            id: '2',
            userId: 'user1',
            title: 'منتج مفضل متاح',
            body: 'المنتج الذي أضفته للمفضلة متاح الآن بسعر مخفض',
            type: NotificationType.favorite,
            createdAt: DateTime.now().subtract(const Duration(hours: 2)),
            isRead: false,
            data: {'productId': 'product2'},
          ),
          NotificationModel(
            id: '3',
            userId: 'user1',
            title: 'تم بيع منتجك',
            body: 'تم بيع "لابتوب ديل XPS" بنجاح',
            type: NotificationType.sale,
            createdAt: DateTime.now().subtract(const Duration(hours: 4)),
            isRead: true,
            data: {'productId': 'product3'},
          ),
          NotificationModel(
            id: '4',
            userId: 'user1',
            title: 'عرض جديد',
            body: 'خصم 20% على جميع الإلكترونيات لفترة محدودة',
            type: NotificationType.promotion,
            createdAt: DateTime.now().subtract(const Duration(days: 1)),
            isRead: true,
            data: {'category': 'electronics'},
          ),
          NotificationModel(
            id: '5',
            userId: 'user1',
            title: 'تحديث التطبيق',
            body: 'إصدار جديد من التطبيق متاح للتحميل',
            type: NotificationType.system,
            createdAt: DateTime.now().subtract(const Duration(days: 2)),
            isRead: false,
            data: {'version': '1.1.0'},
          ),
        ];
        
        _unreadNotifications = _allNotifications.where((n) => !n.isRead).toList();
        _isLoading = false;
      });
    });
  }

  void _markAsRead(NotificationModel notification) {
    setState(() {
      final index = _allNotifications.indexWhere((n) => n.id == notification.id);
      if (index != -1) {
        _allNotifications[index] = notification.copyWith(isRead: true);
        _unreadNotifications.removeWhere((n) => n.id == notification.id);
      }
    });
  }

  void _markAllAsRead() {
    setState(() {
      _allNotifications = _allNotifications.map((n) => n.copyWith(isRead: true)).toList();
      _unreadNotifications.clear();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديد جميع الإشعارات كمقروءة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _deleteNotification(NotificationModel notification) {
    setState(() {
      _allNotifications.removeWhere((n) => n.id == notification.id);
      _unreadNotifications.removeWhere((n) => n.id == notification.id);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم حذف الإشعار'),
        backgroundColor: AppColors.success,
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () {
            setState(() {
              _allNotifications.add(notification);
              if (!notification.isRead) {
                _unreadNotifications.add(notification);
              }
            });
          },
        ),
      ),
    );
  }

  void _handleNotificationTap(NotificationModel notification) {
    if (!notification.isRead) {
      _markAsRead(notification);
    }
    
    // Handle navigation based on notification type
    switch (notification.type) {
      case NotificationType.message:
        // Navigate to chat
        break;
      case NotificationType.favorite:
        // Navigate to product details
        break;
      case NotificationType.sale:
        // Navigate to my products
        break;
      case NotificationType.promotion:
        // Navigate to category or offers
        break;
      case NotificationType.system:
        // Show system message or navigate to settings
        break;
      case NotificationType.reminder:
        // Handle reminder
        break;
      case NotificationType.security:
        // Handle security notification
        break;
      case NotificationType.update:
        // Handle update notification
        break;
      case NotificationType.productInterest:
        // Navigate to product details
        break;
      case NotificationType.review:
        // Navigate to reviews
        break;
      case NotificationType.priceAlert:
        // Navigate to product details
        break;
      case NotificationType.order:
        // Navigate to order details
        break;
      case NotificationType.product:
        // Navigate to product details
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('الإشعارات'),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          if (_unreadNotifications.isNotEmpty)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text('تحديد الكل كمقروء'),
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('الكل'),
                  const SizedBox(width: 4),
                  if (_allNotifications.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: const BoxDecoration(
                        color: AppColors.textLight,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        _allNotifications.length.toString(),
                        style: const TextStyle(
                          color: AppColors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('غير مقروءة'),
                  const SizedBox(width: 4),
                  if (_unreadNotifications.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: const BoxDecoration(
                        color: AppColors.accent,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        _unreadNotifications.length.toString(),
                        style: const TextStyle(
                          color: AppColors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildNotificationsList(_allNotifications),
                _buildNotificationsList(_unreadNotifications),
              ],
            ),
    );
  }

  Widget _buildNotificationsList(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.notifications_outlined,
              size: 60,
              color: AppColors.textLight,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد إشعارات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا جميع الإشعارات الجديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: AppColors.error,
        child: const Icon(
          Icons.delete,
          color: AppColors.white,
        ),
      ),
      onDismissed: (_) => _deleteNotification(notification),
      child: Card(
        margin: const EdgeInsets.only(bottom: 8),
        color: notification.isRead ? AppColors.surface : AppColors.primary.withValues(alpha: 0.05),
        child: ListTile(
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: _getNotificationColor(notification.type).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getNotificationIcon(notification.type),
              color: _getNotificationColor(notification.type),
            ),
          ),
          title: Text(
            notification.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.w600,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                notification.body,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                notification.timeAgo,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textLight,
                ),
              ),
            ],
          ),
          trailing: notification.isRead
              ? null
              : Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: AppColors.accent,
                    shape: BoxShape.circle,
                  ),
                ),
          onTap: () => _handleNotificationTap(notification),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.message:
        return Icons.chat_bubble_outline;
      case NotificationType.favorite:
        return Icons.favorite_outline;
      case NotificationType.sale:
        return Icons.shopping_bag_outlined;
      case NotificationType.promotion:
        return Icons.local_offer_outlined;
      case NotificationType.system:
        return Icons.info_outline;
      case NotificationType.reminder:
        return Icons.alarm_outlined;
      case NotificationType.security:
        return Icons.security_outlined;
      case NotificationType.update:
        return Icons.system_update_outlined;
      case NotificationType.productInterest:
        return Icons.shopping_cart_outlined;
      case NotificationType.review:
        return Icons.star_outline;
      case NotificationType.priceAlert:
        return Icons.trending_down_outlined;
      case NotificationType.order:
        return Icons.receipt_outlined;
      case NotificationType.product:
        return Icons.inventory_outlined;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.message:
        return AppColors.primary;
      case NotificationType.favorite:
        return AppColors.accent;
      case NotificationType.sale:
        return AppColors.success;
      case NotificationType.promotion:
        return AppColors.warning;
      case NotificationType.system:
        return AppColors.info;
      case NotificationType.reminder:
        return AppColors.secondary;
      case NotificationType.security:
        return AppColors.error;
      case NotificationType.update:
        return AppColors.info;
      case NotificationType.productInterest:
        return AppColors.primary;
      case NotificationType.review:
        return AppColors.warning;
      case NotificationType.priceAlert:
        return AppColors.success;
      case NotificationType.order:
        return AppColors.primary;
      case NotificationType.product:
        return AppColors.secondary;
    }
  }
}
