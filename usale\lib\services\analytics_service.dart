import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/analytics_model.dart';
// import '../services/database_service.dart'; // Removed unused import
import '../services/auth_service.dart';

class AnalyticsService extends ChangeNotifier {
  // Singleton pattern
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  // final DatabaseService _databaseService = DatabaseService(); // Removed unused field
  final AuthService _authService = AuthService();

  UserAnalytics? _userAnalytics;
  ProductAnalytics? _productAnalytics;
  SalesAnalytics? _salesAnalytics;
  List<AnalyticsEvent> _events = [];
  bool _isLoading = false;

  // Getters
  UserAnalytics? get userAnalytics => _userAnalytics;
  ProductAnalytics? get productAnalytics => _productAnalytics;
  SalesAnalytics? get salesAnalytics => _salesAnalytics;
  List<AnalyticsEvent> get events => _events;
  bool get isLoading => _isLoading;

  // Initialize analytics
  Future<void> initialize() async {
    await loadUserAnalytics();
    await loadProductAnalytics();
    await loadSalesAnalytics();
  }

  // Track event
  Future<void> trackEvent({
    required String eventName,
    required EventCategory category,
    Map<String, dynamic>? parameters,
  }) async {
    final currentUser = _authService.currentUser;
    
    try {
      final event = AnalyticsEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser?.id,
        eventName: eventName,
        category: category,
        parameters: parameters ?? {},
        timestamp: DateTime.now(),
      );

      _events.insert(0, event);
      
      // Keep only last 1000 events
      if (_events.length > 1000) {
        _events = _events.take(1000).toList();
      }

      notifyListeners();

      // In real app, send to analytics service
      debugPrint('Analytics Event: $eventName - $parameters');
    } catch (e) {
      debugPrint('Error tracking event: $e');
    }
  }

  // Track screen view
  Future<void> trackScreenView(String screenName) async {
    await trackEvent(
      eventName: 'screen_view',
      category: EventCategory.navigation,
      parameters: {'screen_name': screenName},
    );
  }

  // Track product view
  Future<void> trackProductView(String productId, String productTitle) async {
    await trackEvent(
      eventName: 'product_view',
      category: EventCategory.product,
      parameters: {
        'product_id': productId,
        'product_title': productTitle,
      },
    );
  }

  // Track search
  Future<void> trackSearch(String query, int resultsCount) async {
    await trackEvent(
      eventName: 'search',
      category: EventCategory.search,
      parameters: {
        'search_query': query,
        'results_count': resultsCount,
      },
    );
  }

  // Track purchase
  Future<void> trackPurchase({
    required String productId,
    required String productTitle,
    required double amount,
    required String currency,
  }) async {
    await trackEvent(
      eventName: 'purchase',
      category: EventCategory.ecommerce,
      parameters: {
        'product_id': productId,
        'product_title': productTitle,
        'amount': amount,
        'currency': currency,
      },
    );
  }

  // Track add to favorites
  Future<void> trackAddToFavorites(String productId) async {
    await trackEvent(
      eventName: 'add_to_favorites',
      category: EventCategory.engagement,
      parameters: {'product_id': productId},
    );
  }

  // Track message sent
  Future<void> trackMessageSent(String chatId) async {
    await trackEvent(
      eventName: 'message_sent',
      category: EventCategory.social,
      parameters: {'chat_id': chatId},
    );
  }

  // Load user analytics
  Future<void> loadUserAnalytics() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // In real app, calculate from database
      _userAnalytics = await _getMockUserAnalytics(currentUser.id);
    } catch (e) {
      debugPrint('Error loading user analytics: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load product analytics
  Future<void> loadProductAnalytics() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    try {
      // In real app, calculate from database
      _productAnalytics = await _getMockProductAnalytics(currentUser.id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading product analytics: $e');
    }
  }

  // Load sales analytics
  Future<void> loadSalesAnalytics() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    try {
      // In real app, calculate from database
      _salesAnalytics = await _getMockSalesAnalytics(currentUser.id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading sales analytics: $e');
    }
  }

  // Get analytics for date range
  Future<AnalyticsData> getAnalyticsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // Filter events by date range
      final filteredEvents = _events.where((event) {
        return event.timestamp.isAfter(startDate) && 
               event.timestamp.isBefore(endDate);
      }).toList();

      // Calculate metrics
      final totalEvents = filteredEvents.length;
      final uniqueUsers = filteredEvents
          .where((e) => e.userId != null)
          .map((e) => e.userId)
          .toSet()
          .length;

      final eventsByCategory = <EventCategory, int>{};
      for (final event in filteredEvents) {
        eventsByCategory[event.category] = 
            (eventsByCategory[event.category] ?? 0) + 1;
      }

      return AnalyticsData(
        totalEvents: totalEvents,
        uniqueUsers: uniqueUsers,
        eventsByCategory: eventsByCategory,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      debugPrint('Error getting analytics for date range: $e');
      return AnalyticsData(
        totalEvents: 0,
        uniqueUsers: 0,
        eventsByCategory: {},
        startDate: startDate,
        endDate: endDate,
      );
    }
  }

  // Get top products
  List<ProductPerformance> getTopProducts({int limit = 10}) {
    final productViews = <String, int>{};
    final productTitles = <String, String>{};

    for (final event in _events) {
      if (event.eventName == 'product_view') {
        final productId = event.parameters['product_id'] as String?;
        final productTitle = event.parameters['product_title'] as String?;
        
        if (productId != null) {
          productViews[productId] = (productViews[productId] ?? 0) + 1;
          if (productTitle != null) {
            productTitles[productId] = productTitle;
          }
        }
      }
    }

    final sortedProducts = productViews.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedProducts.take(limit).map((entry) {
      return ProductPerformance(
        productId: entry.key,
        productTitle: productTitles[entry.key] ?? 'منتج غير معروف',
        views: entry.value,
        favorites: 0, // Calculate from favorites events
        purchases: 0, // Calculate from purchase events
      );
    }).toList();
  }

  // Get user engagement metrics
  UserEngagement getUserEngagement() {
    final now = DateTime.now();
    final last7Days = now.subtract(const Duration(days: 7));
    final last30Days = now.subtract(const Duration(days: 30));

    final events7Days = _events.where((e) => e.timestamp.isAfter(last7Days)).length;
    final events30Days = _events.where((e) => e.timestamp.isAfter(last30Days)).length;

    final avgSessionLength = _calculateAverageSessionLength();
    final bounceRate = _calculateBounceRate();

    return UserEngagement(
      eventsLast7Days: events7Days,
      eventsLast30Days: events30Days,
      averageSessionLength: avgSessionLength,
      bounceRate: bounceRate,
    );
  }

  // Calculate average session length
  Duration _calculateAverageSessionLength() {
    // Simplified calculation
    // In real app, track session start/end events
    return const Duration(minutes: 8, seconds: 30);
  }

  // Calculate bounce rate
  double _calculateBounceRate() {
    // Simplified calculation
    // In real app, calculate based on single-page sessions
    return 0.35; // 35%
  }

  // Mock data methods
  Future<UserAnalytics> _getMockUserAnalytics(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return UserAnalytics(
      userId: userId,
      totalViews: 1250,
      totalProducts: 8,
      totalSales: 15,
      totalRevenue: 12500.0,
      averageRating: 4.7,
      totalReviews: 23,
      profileViews: 89,
      favoriteCount: 156,
      messagesSent: 45,
      messagesReceived: 67,
      lastActiveAt: DateTime.now().subtract(const Duration(minutes: 15)),
    );
  }

  Future<ProductAnalytics> _getMockProductAnalytics(String userId) async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    return ProductAnalytics(
      totalProducts: 8,
      activeProducts: 6,
      soldProducts: 2,
      totalViews: 1250,
      totalFavorites: 156,
      averageViews: 156.25,
      topCategory: 'إلكترونيات',
      bestPerformingProduct: 'آيفون 15 برو ماكس',
      conversionRate: 0.12, // 12%
    );
  }

  Future<SalesAnalytics> _getMockSalesAnalytics(String userId) async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    return SalesAnalytics(
      totalSales: 15,
      totalRevenue: 12500.0,
      averageOrderValue: 833.33,
      salesThisMonth: 5,
      revenueThisMonth: 4200.0,
      salesLastMonth: 7,
      revenueLastMonth: 5800.0,
      topSellingCategory: 'إلكترونيات',
      salesGrowth: -0.28, // -28%
      revenueGrowth: -0.27, // -27%
    );
  }

  // Clear analytics data
  void clearAnalytics() {
    _events.clear();
    _userAnalytics = null;
    _productAnalytics = null;
    _salesAnalytics = null;
    notifyListeners();
  }
}
