import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class SearchFiltersWidget extends StatefulWidget {
  final String? selectedCategory;
  final String? selectedCondition;
  final String? selectedLocation;
  final double? minPrice;
  final double? maxPrice;
  final String sortBy;
  final Function(Map<String, dynamic>) onFiltersChanged;

  const SearchFiltersWidget({
    super.key,
    this.selectedCategory,
    this.selectedCondition,
    this.selectedLocation,
    this.minPrice,
    this.maxPrice,
    required this.sortBy,
    required this.onFiltersChanged,
  });

  @override
  State<SearchFiltersWidget> createState() => _SearchFiltersWidgetState();
}

class _SearchFiltersWidgetState extends State<SearchFiltersWidget> {
  late String? _selectedCategory;
  late String? _selectedCondition;
  late String? _selectedLocation;
  late double? _minPrice;
  late double? _maxPrice;
  late String _sortBy;
  
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
    _selectedCondition = widget.selectedCondition;
    _selectedLocation = widget.selectedLocation;
    _minPrice = widget.minPrice;
    _maxPrice = widget.maxPrice;
    _sortBy = widget.sortBy;
    
    _minPriceController.text = _minPrice?.toString() ?? '';
    _maxPriceController.text = _maxPrice?.toString() ?? '';
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    final filters = {
      'category': _selectedCategory,
      'condition': _selectedCondition,
      'location': _selectedLocation,
      'minPrice': _minPrice,
      'maxPrice': _maxPrice,
      'sortBy': _sortBy,
    };
    widget.onFiltersChanged(filters);
  }

  void _resetFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedCondition = null;
      _selectedLocation = null;
      _minPrice = null;
      _maxPrice = null;
      _sortBy = 'الأحدث';
      _minPriceController.clear();
      _maxPriceController.clear();
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Text(
                  'فلترة النتائج',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('إعادة تعيين'),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Filters Content
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              children: [
                // Category Filter
                _buildFilterSection(
                  'الفئة',
                  _buildCategoryFilter(),
                ),
                const SizedBox(height: 24),
                
                // Condition Filter
                _buildFilterSection(
                  'الحالة',
                  _buildConditionFilter(),
                ),
                const SizedBox(height: 24),
                
                // Price Range Filter
                _buildFilterSection(
                  'نطاق السعر',
                  _buildPriceRangeFilter(),
                ),
                const SizedBox(height: 24),
                
                // Location Filter
                _buildFilterSection(
                  'الموقع',
                  _buildLocationFilter(),
                ),
                const SizedBox(height: 24),
                
                // Sort Filter
                _buildFilterSection(
                  'ترتيب النتائج',
                  _buildSortFilter(),
                ),
              ],
            ),
          ),
          
          // Apply Button
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _applyFilters();
                  Navigator.pop(context);
                },
                child: const Text('تطبيق الفلاتر'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: AppConstants.categories.map((category) {
        final isSelected = _selectedCategory == category;
        return FilterChip(
          label: Text(category),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedCategory = selected ? category : null;
            });
          },
          backgroundColor: AppColors.surfaceVariant,
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
        );
      }).toList(),
    );
  }

  Widget _buildConditionFilter() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: AppConstants.conditions.map((condition) {
        final isSelected = _selectedCondition == condition;
        return FilterChip(
          label: Text(condition),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedCondition = selected ? condition : null;
            });
          },
          backgroundColor: AppColors.surfaceVariant,
          selectedColor: AppColors.success.withValues(alpha: 0.2),
          checkmarkColor: AppColors.success,
        );
      }).toList(),
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _minPriceController,
                decoration: const InputDecoration(
                  labelText: 'السعر الأدنى',
                  suffixText: 'ريال',
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _minPrice = double.tryParse(value);
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                controller: _maxPriceController,
                decoration: const InputDecoration(
                  labelText: 'السعر الأعلى',
                  suffixText: 'ريال',
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _maxPrice = double.tryParse(value);
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AppConstants.priceRanges.map((range) {
            return ActionChip(
              label: Text(range),
              onPressed: () {
                // Parse price range and set min/max
                _setPriceRange(range);
              },
              backgroundColor: AppColors.surfaceVariant,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLocationFilter() {
    final locations = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة', 'الطائف', 'أبها', 'تبوك'];
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: locations.map((location) {
        final isSelected = _selectedLocation == location;
        return FilterChip(
          label: Text(location),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedLocation = selected ? location : null;
            });
          },
          backgroundColor: AppColors.surfaceVariant,
          selectedColor: AppColors.secondary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.secondary,
        );
      }).toList(),
    );
  }

  Widget _buildSortFilter() {
    return Column(
      children: AppConstants.sortOptions.map((option) {
        return RadioListTile<String>(
          title: Text(option),
          value: option,
          groupValue: _sortBy,
          onChanged: (value) {
            setState(() {
              _sortBy = value!;
            });
          },
          activeColor: AppColors.primary,
        );
      }).toList(),
    );
  }

  void _setPriceRange(String range) {
    setState(() {
      switch (range) {
        case 'أقل من 100':
          _minPrice = null;
          _maxPrice = 100;
          _minPriceController.clear();
          _maxPriceController.text = '100';
          break;
        case '100 - 500':
          _minPrice = 100;
          _maxPrice = 500;
          _minPriceController.text = '100';
          _maxPriceController.text = '500';
          break;
        case '500 - 1000':
          _minPrice = 500;
          _maxPrice = 1000;
          _minPriceController.text = '500';
          _maxPriceController.text = '1000';
          break;
        case '1000 - 5000':
          _minPrice = 1000;
          _maxPrice = 5000;
          _minPriceController.text = '1000';
          _maxPriceController.text = '5000';
          break;
        case '5000 - 10000':
          _minPrice = 5000;
          _maxPrice = 10000;
          _minPriceController.text = '5000';
          _maxPriceController.text = '10000';
          break;
        case 'أكثر من 10000':
          _minPrice = 10000;
          _maxPrice = null;
          _minPriceController.text = '10000';
          _maxPriceController.clear();
          break;
      }
    });
  }
}
