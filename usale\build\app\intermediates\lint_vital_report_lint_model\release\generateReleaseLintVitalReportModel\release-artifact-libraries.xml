<libraries>
  <library
      name="__local_aars__:C:\Users\<USER>\Desktop\Usale\usale\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="C:\Users\<USER>\Desktop\Usale\usale\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:C:\Users\<USER>\Desktop\Usale\usale\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:shared_preferences_android::release"
      project=":shared_preferences_android"/>
  <library
      name=":@@:connectivity_plus::release"
      project=":connectivity_plus"/>
  <library
      name=":@@:device_info_plus::release"
      project=":device_info_plus"/>
  <library
      name=":@@:flutter_local_notifications::release"
      project=":flutter_local_notifications"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      project=":flutter_plugin_android_lifecycle"/>
  <library
      name=":@@:google_sign_in_android::release"
      project=":google_sign_in_android"/>
  <library
      name=":@@:image_picker_android::release"
      project=":image_picker_android"/>
  <library
      name=":@@:package_info_plus::release"
      project=":package_info_plus"/>
  <library
      name=":@@:path_provider_android::release"
      project=":path_provider_android"/>
  <library
      name=":@@:permission_handler_android::release"
      project=":permission_handler_android"/>
  <library
      name=":@@:share_plus::release"
      project=":share_plus"/>
  <library
      name=":@@:sqflite_android::release"
      project=":sqflite_android"/>
  <library
      name=":@@:url_launcher_android::release"
      project=":url_launcher_android"/>
  <library
      name=":@@:video_player_android::release"
      project=":video_player_android"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095\2bdae67d5f76761e92d689bcb16ea23d38879ab0\flutter_embedding_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\25a37e5cf0f4e5220cbf7cafe9249990\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\25a37e5cf0f4e5220cbf7cafe9249990\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.2.0\34dbc21d203cc4d4d623ac572a21acd4ccd716af\collection-1.2.0.jar"
      resolved="androidx.collection:collection:1.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.0\85f8b81009cda5890e54ba67d64b5e599c645020\kotlin-stdlib-2.1.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.0"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095\e0c7d9fa695015a74f8d970cb8e8df87dc35065a\armeabi_v7a_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095\b8e83e0554ed5a594bd5427861aaec9f3ffd870e\arm64_v8a_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095\62a3eec18003c50389891f1bf523cf25205bbbb1\x86_64_release-1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095.jar"
      resolved="io.flutter:x86_64_release:1.0.0-8cd19e509d6bece8ccd74aef027c4ca947363095"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\jars\classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0\jars\classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a1ace20c32bb404148bb3a63ba1bc\transformed\jetified-play-services-tasks-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a1ace20c32bb404148bb3a63ba1bc\transformed\jetified-play-services-tasks-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\fde491953245a6a32bc7896f31b86f18\transformed\jetified-play-services-basement-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\fde491953245a6a32bc7896f31b86f18\transformed\jetified-play-services-basement-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d0ad2c9a7eee0ad2b557032bddebd70\transformed\appcompat-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d0ad2c9a7eee0ad2b557032bddebd70\transformed\appcompat-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-hls:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-dash:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-rtsp:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-rtsp:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e93556932885008eff7df21847fbdad2\transformed\jetified-appcompat-resources-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e93556932885008eff7df21847fbdad2\transformed\jetified-appcompat-resources-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\17952863fa1b6f5dddf3dbb6f4ce2941\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\17952863fa1b6f5dddf3dbb6f4ce2941\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="com.google.code.gson:gson:2.8.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.9\8a432c1d6825781e21a02db2e2c33c5fde2833b9\gson-2.8.9.jar"
      resolved="com.google.code.gson:gson:2.8.9"/>
  <library
      name="com.google.guava:guava:33.0.0-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.0.0-android\cfbbdc54f232feedb85746aeeea0722f5244bb9a\guava-33.0.0-android.jar"
      resolved="com.google.guava:guava:33.0.0-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
</libraries>
