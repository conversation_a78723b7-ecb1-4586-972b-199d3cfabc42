import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';
import '../../../../services/search_service.dart';

import '../widgets/search_filters_widget.dart';
import '../widgets/search_results_widget.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<String> _searchHistory = [];
  List<String> _suggestions = [];


  // Filter variables
  String? _selectedCategory;
  String? _selectedCondition;
  String? _selectedLocation;
  double? _minPrice;
  double? _maxPrice;
  String _sortBy = 'الأحدث';

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    _loadSuggestions();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadSearchHistory() {
    // Load from local storage
    setState(() {
      _searchHistory = [
        'آيفون 15',
        'سيارة مستعملة',
        'شقة للإيجار',
        'لابتوب',
        'ساعة ذكية',
      ];
    });
  }

  void _loadSuggestions() {
    setState(() {
      _suggestions = [
        'آيفون 15 برو',
        'سامسونج جالاكسي',
        'سيارة تويوتا',
        'شقة في الرياض',
        'لابتوب ديل',
        'ساعة أبل',
        'كاميرا كانون',
        'دراجة هوائية',
      ];
    });
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    final searchService = Provider.of<SearchService>(context, listen: false);

    // Create filters from current state
    final filters = SearchFilters(
      category: _selectedCategory,
      condition: _selectedCondition,
      location: _selectedLocation,
      minPrice: _minPrice,
      maxPrice: _maxPrice,
    );

    // Determine sort option
    SortOption sortOption = SortOption.newest;
    switch (_sortBy) {
      case 'الأقدم':
        sortOption = SortOption.oldest;
        break;
      case 'السعر: من الأقل للأعلى':
        sortOption = SortOption.priceLowToHigh;
        break;
      case 'السعر: من الأعلى للأقل':
        sortOption = SortOption.priceHighToLow;
        break;
      case 'الأكثر مشاهدة':
        sortOption = SortOption.mostViewed;
        break;
      case 'الأعلى تقييماً':
        sortOption = SortOption.mostFavorited;
        break;
    }

    searchService.searchProducts(
      query: query.trim(),
      filters: filters,
      sortOption: sortOption,
    );
  }



  void _showFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SearchFiltersWidget(
        selectedCategory: _selectedCategory,
        selectedCondition: _selectedCondition,
        selectedLocation: _selectedLocation,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        sortBy: _sortBy,
        onFiltersChanged: (filters) {
          setState(() {
            _selectedCategory = filters['category'];
            _selectedCondition = filters['condition'];
            _selectedLocation = filters['location'];
            _minPrice = filters['minPrice'];
            _maxPrice = filters['maxPrice'];
            _sortBy = filters['sortBy'];
          });

          // Re-search with new filters
          if (_searchController.text.isNotEmpty) {
            _performSearch(_searchController.text);
          }
        },
      ),
    );
  }

  void _clearSearchHistory() {
    setState(() {
      _searchHistory.clear();
    });
  }

  bool _hasActiveFilters() {
    return _selectedCategory != null ||
           _selectedCondition != null ||
           _selectedLocation != null ||
           _minPrice != null ||
           _maxPrice != null ||
           _sortBy != 'الأحدث';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('البحث'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: AppColors.surface,
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: AppConstants.searchPlaceholder,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_searchController.text.isNotEmpty)
                      IconButton(
                        onPressed: () {
                          _searchController.clear();
                          Provider.of<SearchService>(context, listen: false).clearSearch();
                        },
                        icon: const Icon(Icons.clear),
                      ),
                    IconButton(
                      onPressed: _showFilters,
                      icon: Stack(
                        children: [
                          const Icon(Icons.tune),
                          if (_hasActiveFilters())
                            Positioned(
                              top: 0,
                              right: 0,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: AppColors.accent,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              onChanged: (value) {
                setState(() {});
              },
              onSubmitted: _performSearch,
            ),
          ),
          
          // Content
          Expanded(
            child: Consumer<SearchService>(
              builder: (context, searchService, child) {
                if (_searchController.text.isEmpty) {
                  return _buildSearchSuggestions();
                }

                return SearchResultsWidget(
                  products: searchService.searchResults,
                  isLoading: searchService.isSearching,
                  searchQuery: _searchController.text,
                  onLoadMore: () {
                    // Load more results
                  },
                  hasMore: false,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return ListView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      children: [
        // Search History
        if (_searchHistory.isNotEmpty) ...[
          Row(
            children: [
              Text(
                'عمليات البحث الأخيرة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _clearSearchHistory,
                child: const Text('مسح الكل'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...(_searchHistory.map((query) => ListTile(
                leading: const Icon(Icons.history, color: AppColors.textLight),
                title: Text(query),
                trailing: IconButton(
                  onPressed: () {
                    setState(() {
                      _searchHistory.remove(query);
                    });
                  },
                  icon: const Icon(Icons.close, size: 20),
                ),
                onTap: () {
                  _searchController.text = query;
                  _performSearch(query);
                },
              ))),
          const SizedBox(height: 24),
        ],
        
        // Popular Searches
        Text(
          'البحث الشائع',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _suggestions.map((suggestion) {
            return ActionChip(
              label: Text(suggestion),
              onPressed: () {
                _searchController.text = suggestion;
                _performSearch(suggestion);
              },
              backgroundColor: AppColors.surfaceVariant,
              labelStyle: const TextStyle(
                color: AppColors.textPrimary,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }



  @override
  bool get wantKeepAlive => true;
}
