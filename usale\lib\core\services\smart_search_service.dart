import 'package:flutter/material.dart';
import 'dart:math';

/// خدمة البحث الذكي بالذكاء الاصطناعي
class SmartSearchService extends ChangeNotifier {
  static final SmartSearchService _instance = SmartSearchService._internal();
  factory SmartSearchService() => _instance;
  SmartSearchService._internal();

  // بيانات البحث
  List<SearchResult> _searchResults = [];
  List<String> _searchHistory = [];
  List<String> _popularSearches = [];
  Map<String, int> _searchFrequency = {};
  bool _isSearching = false;
  String _lastQuery = '';

  // خوارزمية الذكاء الاصطناعي للبحث
  final AISearchEngine _aiEngine = AISearchEngine();

  // Getters
  List<SearchResult> get searchResults => _searchResults;
  List<String> get searchHistory => _searchHistory;
  List<String> get popularSearches => _popularSearches;
  bool get isSearching => _isSearching;
  String get lastQuery => _lastQuery;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadSearchData();
    _generatePopularSearches();
  }

  /// تحميل بيانات البحث المحفوظة
  Future<void> _loadSearchData() async {
    // محاكاة تحميل البيانات
    _searchHistory = [
      'آيفون 15',
      'سيارة تويوتا',
      'شقة للإيجار',
      'لابتوب ديل',
      'كاميرا كانون',
    ];
    
    _searchFrequency = {
      'آيفون': 150,
      'سيارة': 120,
      'شقة': 100,
      'لابتوب': 80,
      'كاميرا': 60,
      'ساعة': 45,
      'هاتف': 200,
      'تلفزيون': 70,
    };
  }

  /// إنشاء قائمة البحثات الشائعة
  void _generatePopularSearches() {
    final sortedSearches = _searchFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    _popularSearches = sortedSearches
        .take(10)
        .map((e) => e.key)
        .toList();
  }

  /// البحث الذكي
  Future<List<SearchResult>> smartSearch(String query) async {
    if (query.trim().isEmpty) {
      _searchResults = [];
      notifyListeners();
      return _searchResults;
    }

    _isSearching = true;
    _lastQuery = query;
    notifyListeners();

    try {
      // إضافة إلى تاريخ البحث
      _addToSearchHistory(query);
      
      // تحليل الاستعلام بالذكاء الاصطناعي
      final analyzedQuery = await _aiEngine.analyzeQuery(query);
      
      // البحث في قاعدة البيانات
      final results = await _performSearch(analyzedQuery);
      
      // ترتيب النتائج بالذكاء الاصطناعي
      final rankedResults = await _aiEngine.rankResults(results, analyzedQuery);
      
      _searchResults = rankedResults;
      
    } catch (e) {
      _searchResults = [];
    } finally {
      _isSearching = false;
      notifyListeners();
    }

    return _searchResults;
  }

  /// إضافة إلى تاريخ البحث
  void _addToSearchHistory(String query) {
    if (!_searchHistory.contains(query)) {
      _searchHistory.insert(0, query);
      if (_searchHistory.length > 20) {
        _searchHistory.removeLast();
      }
    }
    
    // تحديث تكرار البحث
    _searchFrequency[query] = (_searchFrequency[query] ?? 0) + 1;
    _generatePopularSearches();
  }

  /// تنفيذ البحث الفعلي
  Future<List<SearchResult>> _performSearch(AnalyzedQuery analyzedQuery) async {
    // محاكاة البحث في قاعدة البيانات
    await Future.delayed(const Duration(milliseconds: 500));
    
    final mockResults = _generateMockResults(analyzedQuery.originalQuery);
    return mockResults;
  }

  /// إنشاء نتائج وهمية للاختبار
  List<SearchResult> _generateMockResults(String query) {
    final categories = ['إلكترونيات', 'مركبات', 'عقارات', 'أزياء', 'أثاث'];
    final products = [
      'آيفون 15 برو ماكس',
      'سامسونج جالاكسي S24',
      'لابتوب ديل XPS',
      'تويوتا كامري 2024',
      'هوندا أكورد',
      'شقة 3 غرف',
      'فيلا للبيع',
      'ساعة أبل',
      'كاميرا كانون',
      'تلفزيون سامسونج',
    ];

    final results = <SearchResult>[];
    final random = Random();

    for (int i = 0; i < 10; i++) {
      final product = products[random.nextInt(products.length)];
      final category = categories[random.nextInt(categories.length)];
      final price = (random.nextInt(10000) + 100) * 10;
      
      results.add(SearchResult(
        id: 'product_$i',
        title: product,
        description: 'وصف تفصيلي للمنتج $product في فئة $category',
        category: category,
        price: price.toDouble(),
        imageUrl: 'https://via.placeholder.com/300x200',
        location: 'الرياض، السعودية',
        rating: 3.5 + random.nextDouble() * 1.5,
        relevanceScore: random.nextDouble(),
        isPromoted: random.nextBool(),
        tags: _generateTags(product),
      ));
    }

    return results;
  }

  /// إنشاء علامات للمنتج
  List<String> _generateTags(String product) {
    final allTags = ['جديد', 'مستعمل', 'عرض خاص', 'سعر مميز', 'توصيل مجاني'];
    final random = Random();
    final numTags = random.nextInt(3) + 1;
    
    final tags = <String>[];
    for (int i = 0; i < numTags; i++) {
      final tag = allTags[random.nextInt(allTags.length)];
      if (!tags.contains(tag)) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// الحصول على اقتراحات البحث
  List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) return _popularSearches.take(5).toList();
    
    final suggestions = <String>[];
    
    // البحث في التاريخ
    for (final item in _searchHistory) {
      if (item.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(item);
      }
    }
    
    // البحث في الشائعة
    for (final item in _popularSearches) {
      if (item.toLowerCase().contains(query.toLowerCase()) && 
          !suggestions.contains(item)) {
        suggestions.add(item);
      }
    }
    
    return suggestions.take(8).toList();
  }

  /// مسح تاريخ البحث
  void clearSearchHistory() {
    _searchHistory.clear();
    notifyListeners();
  }

  /// حذف عنصر من تاريخ البحث
  void removeFromHistory(String query) {
    _searchHistory.remove(query);
    notifyListeners();
  }
}

/// محرك الذكاء الاصطناعي للبحث
class AISearchEngine {
  /// تحليل استعلام البحث
  Future<AnalyzedQuery> analyzeQuery(String query) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    // تحليل بسيط للاستعلام
    final words = query.toLowerCase().split(' ');
    final categories = _detectCategories(words);
    final priceRange = _detectPriceRange(query);
    final location = _detectLocation(words);
    final intent = _detectIntent(words);
    
    return AnalyzedQuery(
      originalQuery: query,
      processedQuery: query.toLowerCase().trim(),
      categories: categories,
      priceRange: priceRange,
      location: location,
      intent: intent,
      keywords: words,
    );
  }

  /// ترتيب النتائج بالذكاء الاصطناعي
  Future<List<SearchResult>> rankResults(
    List<SearchResult> results, 
    AnalyzedQuery query
  ) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    // حساب نقاط الصلة
    for (final result in results) {
      result.relevanceScore = _calculateRelevanceScore(result, query);
    }
    
    // ترتيب النتائج
    results.sort((a, b) {
      // المنتجات المروجة أولاً
      if (a.isPromoted && !b.isPromoted) return -1;
      if (!a.isPromoted && b.isPromoted) return 1;
      
      // ثم حسب نقاط الصلة
      return b.relevanceScore.compareTo(a.relevanceScore);
    });
    
    return results;
  }

  /// كشف الفئات من الكلمات
  List<String> _detectCategories(List<String> words) {
    final categoryKeywords = {
      'إلكترونيات': ['هاتف', 'آيفون', 'سامسونج', 'لابتوب', 'كمبيوتر', 'تلفزيون'],
      'مركبات': ['سيارة', 'تويوتا', 'هوندا', 'بي إم دبليو', 'مرسيدس'],
      'عقارات': ['شقة', 'فيلا', 'بيت', 'أرض', 'مكتب'],
      'أزياء': ['ملابس', 'حذاء', 'حقيبة', 'ساعة'],
    };
    
    final detectedCategories = <String>[];
    
    for (final category in categoryKeywords.keys) {
      for (final keyword in categoryKeywords[category]!) {
        if (words.any((word) => word.contains(keyword))) {
          detectedCategories.add(category);
          break;
        }
      }
    }
    
    return detectedCategories;
  }

  /// كشف نطاق السعر
  PriceRange? _detectPriceRange(String query) {
    final priceRegex = RegExp(r'(\d+)\s*-\s*(\d+)');
    final match = priceRegex.firstMatch(query);
    
    if (match != null) {
      final min = double.tryParse(match.group(1)!) ?? 0;
      final max = double.tryParse(match.group(2)!) ?? double.infinity;
      return PriceRange(min: min, max: max);
    }
    
    return null;
  }

  /// كشف الموقع
  String? _detectLocation(List<String> words) {
    final locations = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة'];
    
    for (final location in locations) {
      if (words.any((word) => word.contains(location.toLowerCase()))) {
        return location;
      }
    }
    
    return null;
  }

  /// كشف نية البحث
  SearchIntent _detectIntent(List<String> words) {
    if (words.any((word) => ['شراء', 'أشتري', 'أريد'].contains(word))) {
      return SearchIntent.buy;
    }
    if (words.any((word) => ['بيع', 'أبيع'].contains(word))) {
      return SearchIntent.sell;
    }
    if (words.any((word) => ['إيجار', 'أجر'].contains(word))) {
      return SearchIntent.rent;
    }
    
    return SearchIntent.browse;
  }

  /// حساب نقاط الصلة
  double _calculateRelevanceScore(SearchResult result, AnalyzedQuery query) {
    double score = 0.0;
    
    // مطابقة العنوان
    final titleWords = result.title.toLowerCase().split(' ');
    for (final keyword in query.keywords) {
      if (titleWords.any((word) => word.contains(keyword))) {
        score += 0.3;
      }
    }
    
    // مطابقة الفئة
    if (query.categories.contains(result.category)) {
      score += 0.2;
    }
    
    // مطابقة الموقع
    if (query.location != null && result.location.contains(query.location!)) {
      score += 0.1;
    }
    
    // التقييم
    score += result.rating * 0.1;
    
    // عشوائية للتنويع
    score += Random().nextDouble() * 0.1;
    
    return score.clamp(0.0, 1.0);
  }
}

/// نموذج الاستعلام المحلل
class AnalyzedQuery {
  final String originalQuery;
  final String processedQuery;
  final List<String> categories;
  final PriceRange? priceRange;
  final String? location;
  final SearchIntent intent;
  final List<String> keywords;

  AnalyzedQuery({
    required this.originalQuery,
    required this.processedQuery,
    required this.categories,
    this.priceRange,
    this.location,
    required this.intent,
    required this.keywords,
  });
}

/// نموذج نتيجة البحث
class SearchResult {
  final String id;
  final String title;
  final String description;
  final String category;
  final double price;
  final String imageUrl;
  final String location;
  final double rating;
  final bool isPromoted;
  final List<String> tags;
  double relevanceScore;

  SearchResult({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.price,
    required this.imageUrl,
    required this.location,
    required this.rating,
    required this.isPromoted,
    required this.tags,
    this.relevanceScore = 0.0,
  });
}

/// نطاق السعر
class PriceRange {
  final double min;
  final double max;

  PriceRange({required this.min, required this.max});
}

/// نية البحث
enum SearchIntent {
  buy,    // شراء
  sell,   // بيع
  rent,   // إيجار
  browse, // تصفح
}
