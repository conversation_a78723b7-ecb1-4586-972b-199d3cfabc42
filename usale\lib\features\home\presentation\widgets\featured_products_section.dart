import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';

class FeaturedProductsSection extends StatelessWidget {
  const FeaturedProductsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'المنتجات المميزة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // Navigate to all featured products
                },
                child: const Text('عرض الكل'),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            itemCount: _mockFeaturedProducts.length,
            itemBuilder: (context, index) {
              final product = _mockFeaturedProducts[index];
              return _buildFeaturedProductCard(context, product);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedProductCard(BuildContext context, ProductModel product) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          Stack(
            children: [
              Container(
                height: 140,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.lightGrey,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.defaultRadius),
                    topRight: Radius.circular(AppConstants.defaultRadius),
                  ),
                ),
                child: const Icon(
                  Icons.image,
                  size: 40,
                  color: AppColors.textLight,
                ),
              ),
              // Featured Badge
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.accent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مميز',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              // Favorite Button
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.9),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () {
                      // Toggle favorite
                    },
                    icon: const Icon(
                      Icons.favorite_outline,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // Product Info
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    product.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  
                  // Price
                  Text(
                    product.formattedPrice,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  
                  // Location and Time
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on_outlined,
                        size: 14,
                        color: AppColors.textLight,
                      ),
                      const SizedBox(width: 2),
                      Expanded(
                        child: Text(
                          product.location,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    product.timeAgo,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textLight,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Contact Button
                  SizedBox(
                    width: double.infinity,
                    height: 32,
                    child: ElevatedButton(
                      onPressed: () {
                        // Contact seller
                      },
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.zero,
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                      child: const Text('تواصل'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static final List<ProductModel> _mockFeaturedProducts = [
    ProductModel(
      id: '1',
      title: 'آيفون 15 برو ماكس 256 جيجا',
      description: 'جهاز جديد بالكرتون، لون تيتانيوم طبيعي',
      price: 4500,
      category: 'إلكترونيات',
      condition: 'جديد',
      location: 'الرياض',
      sellerId: 'seller1',
      sellerName: 'أحمد محمد',
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      isFeatured: true,
    ),
    ProductModel(
      id: '2',
      title: 'سيارة تويوتا كامري 2022',
      description: 'سيارة نظيفة، صيانة منتظمة، بحالة ممتازة',
      price: 85000,
      category: 'سيارات',
      condition: 'مستعمل - ممتاز',
      location: 'جدة',
      sellerId: 'seller2',
      sellerName: 'محمد علي',
      createdAt: DateTime.now().subtract(const Duration(hours: 5)),
      isFeatured: true,
    ),
    ProductModel(
      id: '3',
      title: 'شقة للإيجار 3 غرف وصالة',
      description: 'شقة مفروشة بالكامل، موقع مميز',
      price: 2500,
      category: 'عقارات',
      condition: 'جديد',
      location: 'الدمام',
      sellerId: 'seller3',
      sellerName: 'فاطمة أحمد',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      isFeatured: true,
    ),
  ];
}
