import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/notification_model.dart';

class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback onTap;
  final VoidCallback onMarkAsRead;
  final VoidCallback onDelete;

  const NotificationCard({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: AppColors.error,
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        ),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      onDismissed: (_) => onDelete(),
      child: Card(
        elevation: notification.isRead ? 1 : 3,
        color: notification.isRead 
            ? AppColors.surface 
            : AppColors.primary.withValues(alpha: 0.05),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          side: notification.isRead 
              ? BorderSide.none 
              : BorderSide(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Notification Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _getNotificationColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getNotificationIcon(),
                    color: _getNotificationColor(),
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // Notification Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and Time
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: TextStyle(
                                fontWeight: notification.isRead 
                                    ? FontWeight.w500 
                                    : FontWeight.bold,
                                fontSize: 16,
                                color: AppColors.textPrimary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            notification.timeAgo,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColors.textLight,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // Body
                      Text(
                        notification.body,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Type Badge and Actions
                      Row(
                        children: [
                          // Type Badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getNotificationColor().withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getTypeDisplayName(),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: _getNotificationColor(),
                              ),
                            ),
                          ),

                          const Spacer(),

                          // Actions
                          if (!notification.isRead)
                            GestureDetector(
                              onTap: onMarkAsRead,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Text(
                                  'تحديد كمقروء',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 8),

                // Unread Indicator
                if (!notification.isRead)
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.accent,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon() {
    switch (notification.type) {
      case NotificationType.message:
        return Icons.chat_bubble_outline;
      case NotificationType.favorite:
        return Icons.favorite_outline;
      case NotificationType.sale:
        return Icons.shopping_bag_outlined;
      case NotificationType.promotion:
        return Icons.local_offer_outlined;
      case NotificationType.system:
        return Icons.info_outline;
      case NotificationType.reminder:
        return Icons.alarm_outlined;
      case NotificationType.security:
        return Icons.security_outlined;
      case NotificationType.update:
        return Icons.system_update_outlined;
      case NotificationType.productInterest:
        return Icons.shopping_cart_outlined;
      case NotificationType.review:
        return Icons.star_outline;
      case NotificationType.priceAlert:
        return Icons.trending_down_outlined;
      case NotificationType.order:
        return Icons.receipt_outlined;
      case NotificationType.product:
        return Icons.inventory_outlined;
    }
  }

  Color _getNotificationColor() {
    switch (notification.type) {
      case NotificationType.message:
        return AppColors.primary;
      case NotificationType.favorite:
        return AppColors.accent;
      case NotificationType.sale:
        return AppColors.success;
      case NotificationType.promotion:
        return AppColors.warning;
      case NotificationType.system:
        return AppColors.info;
      case NotificationType.reminder:
        return AppColors.secondary;
      case NotificationType.security:
        return AppColors.error;
      case NotificationType.update:
        return AppColors.info;
      case NotificationType.productInterest:
        return AppColors.primary;
      case NotificationType.review:
        return AppColors.warning;
      case NotificationType.priceAlert:
        return AppColors.success;
      case NotificationType.order:
        return AppColors.primary;
      case NotificationType.product:
        return AppColors.secondary;
    }
  }

  String _getTypeDisplayName() {
    switch (notification.type) {
      case NotificationType.message:
        return 'رسالة';
      case NotificationType.favorite:
        return 'مفضلة';
      case NotificationType.sale:
        return 'مبيعات';
      case NotificationType.promotion:
        return 'عرض';
      case NotificationType.system:
        return 'نظام';
      case NotificationType.reminder:
        return 'تذكير';
      case NotificationType.security:
        return 'أمان';
      case NotificationType.update:
        return 'تحديث';
      case NotificationType.productInterest:
        return 'اهتمام';
      case NotificationType.review:
        return 'تقييم';
      case NotificationType.priceAlert:
        return 'تنبيه سعر';
      case NotificationType.order:
        return 'طلب';
      case NotificationType.product:
        return 'منتج';
    }
  }
}
