import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:go_router/go_router.dart';
import 'core/navigation/app_router.dart';
import 'core/services/smart_theme_service.dart';
import 'core/services/smart_language_service.dart';
import 'core/services/smart_search_service.dart';
import 'core/services/smart_recommendations_service.dart';
import 'core/services/smart_content_detection_service.dart';
import 'core/services/smart_notifications_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تعيين اتجاه الشاشة
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  // تهيئة الخدمات الذكية
  await _initializeSmartServices();

  runApp(const USaleFinalApp());
}

/// تهيئة الخدمات الذكية
Future<void> _initializeSmartServices() async {
  try {
    // تهيئة خدمة السمات الذكية
    await SmartThemeService().initialize();

    // تهيئة خدمة اللغة الذكية
    await SmartLanguageService().initialize();

    // تهيئة خدمة البحث الذكي
    await SmartSearchService().initialize();

    // تهيئة خدمة التوصيات الذكية
    await SmartRecommendationsService().initialize();

    // تهيئة خدمة كشف المحتوى
    await SmartContentDetectionService().initialize();

    // تهيئة خدمة الإشعارات الذكية
    await SmartNotificationsService().initialize();

    debugPrint('✅ تم تهيئة جميع الخدمات الذكية بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة الخدمات الذكية: $e');
  }
}

class USaleFinalApp extends StatelessWidget {
  const USaleFinalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        SmartThemeService(),
        SmartLanguageService(),
      ]),
      builder: (context, child) {
        final themeService = SmartThemeService();
        final languageService = SmartLanguageService();

        return MaterialApp.router(
          title: languageService.getText('app_name'),
          debugShowCheckedModeBanner: false,

          // السمات الذكية
          theme: SmartThemes.lightTheme,
          darkTheme: SmartThemes.darkTheme,
          themeMode: themeService.themeMode,

          // اللغة الذكية
          locale: languageService.currentLocale,
          supportedLocales: SmartLanguageService.supportedLocales,

          // التنقل
          routerConfig: AppRouter.router,

          // بناء التطبيق
          builder: (context, child) {
            return Directionality(
              textDirection: languageService.textDirection,
              child: child ?? const SizedBox(),
            );
          },
        );
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToHome();
  }

  _navigateToHome() async {
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      context.go(AppRoutes.home);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.teal,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.shopping_bag,
                size: 60,
                color: Colors.teal,
              ),
            ),
            const SizedBox(height: 30),
            const Text(
              'يوسيل - USale',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'السوق الإلكتروني الأول في الخليج',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 50),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}

// الصفحة الرئيسية الجديدة مع GoRouter
class USaleHomePage extends StatelessWidget {
  const USaleHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const MainScreen();
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 4; // البدء بالرئيسية (المؤشر 4)

  final List<Widget> _screens = [
    const ProfileScreen(),
    const FavoritesScreen(),
    const AddAdScreen(),
    const SearchScreen(),
    const HomeScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        selectedItemColor: Colors.teal,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الحساب',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'المفضلة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.add_circle, size: 30),
            label: 'انشر إعلان',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'البحث',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
        ],
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _loadSmartContent();
  }

  /// تحميل المحتوى الذكي
  void _loadSmartContent() {
    // تسجيل زيارة الصفحة الرئيسية
    SmartRecommendationsService().recordProductView('home', 'عام');
  }

  @override
  Widget build(BuildContext context) {
    final languageService = SmartLanguageService();
    final themeService = SmartThemeService();
    final notificationsService = SmartNotificationsService();

    return Scaffold(
      appBar: AppBar(
        title: Text(languageService.getText('app_name')),
        actions: [
          // زر تبديل السمة
          IconButton(
            icon: Icon(themeService.getAdaptiveIcon(
              lightIcon: Icons.dark_mode,
              darkIcon: Icons.light_mode,
            )),
            onPressed: () {
              themeService.toggleTheme();
            },
          ),
          // زر الإشعارات مع عداد
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () {
                  context.go(AppRoutes.notifications);
                },
              ),
              if (notificationsService.unreadCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${notificationsService.unreadCount}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة ترحيب
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.teal, Colors.tealAccent],
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageService.getText('welcome'),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    languageService.getText('discover'),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // الفئات
            Text(
              languageService.getText('categories'),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 600, // ارتفاع ثابت لعرض جميع الفئات
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.1,
                children: [
                  _buildCategoryCard(context, languageService.getText('contractors'), Icons.construction, Colors.orange),
                  _buildCategoryCard(context, languageService.getText('electronics'), Icons.laptop, Colors.red),
                  _buildCategoryCard(context, languageService.getText('real_estate'), Icons.home, Colors.green),
                  _buildCategoryCard(context, languageService.getText('vehicles'), Icons.directions_car, Colors.blue),
                  _buildCategoryCard(context, languageService.getText('fashion_family'), Icons.checkroom, Colors.indigo),
                  _buildCategoryCard(context, languageService.getText('animals'), Icons.pets, Colors.brown),
                  _buildCategoryCard(context, languageService.getText('camping'), Icons.outdoor_grill, Colors.green[700]!),
                  _buildCategoryCard(context, languageService.getText('services'), Icons.room_service, Colors.yellow[700]!),
                  _buildCategoryCard(context, languageService.getText('miscellaneous'), Icons.inventory, Colors.amber),
                  _buildCategoryCard(context, languageService.getText('jobs'), Icons.work, Colors.grey),
                  _buildCategoryCard(context, languageService.getText('furniture'), Icons.chair, Colors.brown[600]!),
                  _buildCategoryCard(context, languageService.getText('gifts'), Icons.card_giftcard, Colors.black),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // حالة التطبيق
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green[600],
                    size: 48,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    languageService.getText('app_working'),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    languageService.getText('all_features_ready'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(BuildContext context, String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        // تسجيل التفاعل مع الفئة
        SmartRecommendationsService().recordProductView(title, title);

        // الانتقال إلى صفحة الفئة باستخدام GoRouter
        context.goToCategory(title, icon, color);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 32),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final SmartSearchService _searchService = SmartSearchService();
  final SmartLanguageService _languageService = SmartLanguageService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_languageService.getText('search')),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // شريط البحث الذكي
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: _languageService.getText('search_products'),
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {});
                if (value.isNotEmpty) {
                  _searchService.smartSearch(value);
                }
              },
            ),

            const SizedBox(height: 20),

            // نتائج البحث الذكي
            Expanded(
              child: AnimatedBuilder(
                animation: _searchService,
                builder: (context, child) {
                  if (_searchService.isSearching) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('البحث الذكي جاري...'),
                        ],
                      ),
                    );
                  }

                  if (_searchService.searchResults.isEmpty) {
                    return _buildEmptyState();
                  }

                  return _buildSearchResults();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'ابحث عن المنتجات التي تريدها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          // عرض البحثات الشائعة
          if (_searchService.popularSearches.isNotEmpty) ...[
            const Text(
              'البحثات الشائعة:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _searchService.popularSearches.take(5).map((search) {
                return ActionChip(
                  label: Text(search),
                  onPressed: () {
                    _searchController.text = search;
                    _searchService.smartSearch(search);
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      itemCount: _searchService.searchResults.length,
      itemBuilder: (context, index) {
        final result = _searchService.searchResults[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.image, color: Colors.grey),
            ),
            title: Text(
              result.title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(result.category),
                Text(
                  '${result.price.toStringAsFixed(0)} ريال',
                  style: const TextStyle(
                    color: Colors.teal,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (result.tags.isNotEmpty)
                  Wrap(
                    spacing: 4,
                    children: result.tags.take(2).map((tag) {
                      return Chip(
                        label: Text(
                          tag,
                          style: const TextStyle(fontSize: 10),
                        ),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      );
                    }).toList(),
                  ),
              ],
            ),
            trailing: result.isPromoted
                ? const Icon(Icons.star, color: Colors.amber)
                : null,
            onTap: () {
              // الانتقال إلى تفاصيل المنتج
              context.goToProductDetails(
                id: result.id,
                name: result.title,
                price: '${result.price.toStringAsFixed(0)} ريال',
                image: result.imageUrl,
                description: result.description,
              );
            },
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المفضلة')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد مفضلات بعد', style: TextStyle(fontSize: 18)),
          ],
        ),
      ),
    );
  }
}

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  String _selectedLanguage = 'العربية';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar مخصص مع تدرج
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Colors.teal,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.teal, Colors.tealAccent],
                  ),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(height: 40),
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.white,
                        child: Icon(
                          Icons.person,
                          size: 50,
                          color: Colors.teal,
                        ),
                      ),
                      SizedBox(height: 12),
                      Text(
                        'أحمد محمد',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // محتوى الصفحة
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات المستخدم
                  _buildStatsSection(),

                  const SizedBox(height: 24),

                  // الحساب والملف الشخصي
                  _buildSectionTitle('الحساب والملف الشخصي'),
                  _buildProfileItem(
                    Icons.person_outline,
                    'تعديل الملف الشخصي',
                    'تحديث معلوماتك الشخصية',
                    () => _showEditProfileDialog(),
                  ),
                  _buildProfileItem(
                    Icons.security,
                    'الأمان والخصوصية',
                    'إدارة كلمة المرور والأمان',
                    () => _showSecurityDialog(),
                  ),
                  _buildProfileItem(
                    Icons.payment,
                    'طرق الدفع',
                    'إدارة البطاقات والمحافظ',
                    () => _showPaymentDialog(),
                  ),

                  const SizedBox(height: 24),

                  // الطلبات والمشتريات
                  _buildSectionTitle('الطلبات والمشتريات'),
                  _buildProfileItem(
                    Icons.shopping_bag_outlined,
                    'طلباتي',
                    'عرض جميع طلباتك',
                    () => _showOrdersDialog(),
                  ),
                  _buildProfileItem(
                    Icons.favorite_outline,
                    'المفضلة',
                    'المنتجات المحفوظة',
                    () => _showFavoritesDialog(),
                  ),
                  _buildProfileItem(
                    Icons.history,
                    'سجل المشتريات',
                    'تاريخ جميع مشترياتك',
                    () => _showHistoryDialog(),
                  ),

                  const SizedBox(height: 24),

                  // الإعدادات
                  _buildSectionTitle('الإعدادات'),
                  _buildSwitchItem(
                    Icons.notifications_outlined,
                    'الإشعارات',
                    'تلقي إشعارات العروض والطلبات',
                    _notificationsEnabled,
                    (value) => setState(() => _notificationsEnabled = value),
                  ),
                  _buildSwitchItem(
                    Icons.dark_mode_outlined,
                    'الوضع الليلي',
                    'تفعيل المظهر الداكن',
                    _darkModeEnabled,
                    (value) => setState(() => _darkModeEnabled = value),
                  ),
                  _buildLanguageItem(),

                  const SizedBox(height: 24),

                  // الدعم والمساعدة
                  _buildSectionTitle('الدعم والمساعدة'),
                  _buildProfileItem(
                    Icons.help_outline,
                    'مركز المساعدة',
                    'الأسئلة الشائعة والدعم',
                    () => _showHelpDialog(),
                  ),
                  _buildProfileItem(
                    Icons.phone_outlined,
                    'اتصل بنا',
                    'تواصل مع فريق الدعم',
                    () => _showContactDialog(),
                  ),
                  _buildProfileItem(
                    Icons.star_outline,
                    'قيم التطبيق',
                    'شاركنا رأيك في التطبيق',
                    () => _showRatingDialog(),
                  ),

                  const SizedBox(height: 24),

                  // معلومات التطبيق
                  _buildSectionTitle('معلومات التطبيق'),
                  _buildProfileItem(
                    Icons.info_outline,
                    'حول التطبيق',
                    'الإصدار 1.0.0',
                    () => _showAboutDialog(),
                  ),
                  _buildProfileItem(
                    Icons.description_outlined,
                    'الشروط والأحكام',
                    'اقرأ شروط الاستخدام',
                    () => _showTermsDialog(),
                  ),
                  _buildProfileItem(
                    Icons.privacy_tip_outlined,
                    'سياسة الخصوصية',
                    'كيف نحمي بياناتك',
                    () => _showPrivacyDialog(),
                  ),

                  const SizedBox(height: 32),

                  // زر تسجيل الخروج
                  _buildLogoutButton(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('15', 'الطلبات', Icons.shopping_bag),
          _buildStatItem('8', 'المفضلة', Icons.favorite),
          _buildStatItem('3', 'التقييمات', Icons.star),
        ],
      ),
    );
  }

  Widget _buildStatItem(String number, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: Colors.teal, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          number,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.teal,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  // بناء عنصر الملف الشخصي
  Widget _buildProfileItem(IconData icon, String title, String subtitle, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.teal, size: 20),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
      ),
    );
  }

  // بناء عنصر التبديل
  Widget _buildSwitchItem(IconData icon, String title, String subtitle, bool value, ValueChanged<bool> onChanged) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.teal, size: 20),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: Colors.teal,
        ),
      ),
    );
  }

  // بناء عنصر اللغة
  Widget _buildLanguageItem() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.language, color: Colors.teal, size: 20),
        ),
        title: const Text(
          'اللغة',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          _selectedLanguage,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: () => _showLanguageDialog(),
      ),
    );
  }

  // بناء زر تسجيل الخروج
  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[300]!),
      ),
      child: TextButton(
        onPressed: () => _showLogoutDialog(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.logout, color: Colors.red[600]),
            const SizedBox(width: 8),
            Text(
              'تسجيل الخروج',
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دوال النوافذ المنبثقة
  void _showEditProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الملف الشخصي'),
        content: const Text('سيتم إضافة صفحة تعديل الملف الشخصي قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showSecurityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الأمان والخصوصية'),
        content: const Text('إدارة كلمة المرور وإعدادات الأمان'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showPaymentDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طرق الدفع'),
        content: const Text('إدارة البطاقات والمحافظ الإلكترونية'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showOrdersDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طلباتي'),
        content: const Text('عرض جميع طلباتك السابقة والحالية'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showFavoritesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المفضلة'),
        content: const Text('المنتجات المحفوظة في قائمة المفضلة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سجل المشتريات'),
        content: const Text('تاريخ جميع مشترياتك السابقة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مركز المساعدة'),
        content: const Text('الأسئلة الشائعة ومركز الدعم الفني'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اتصل بنا'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('📞 الهاتف: 920000000'),
            SizedBox(height: 8),
            Text('📧 البريد: <EMAIL>'),
            SizedBox(height: 8),
            Text('🕒 ساعات العمل: 24/7'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قيم التطبيق'),
        content: const Text('شاركنا رأيك وساعدنا في تحسين التطبيق'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لاحقاً'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('شكراً لك! تم إرسال تقييمك')),
              );
            },
            child: const Text('قيم الآن'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('العربية'),
              leading: Radio<String>(
                value: 'العربية',
                groupValue: _selectedLanguage,
                onChanged: (value) {
                  setState(() => _selectedLanguage = value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('English'),
              leading: Radio<String>(
                value: 'English',
                groupValue: _selectedLanguage,
                onChanged: (value) {
                  setState(() => _selectedLanguage = value!);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('يوسيل - USale'),
            SizedBox(height: 8),
            Text('الإصدار: 1.0.0'),
            SizedBox(height: 8),
            Text('السوق الإلكتروني الأول في الخليج'),
            SizedBox(height: 8),
            Text('© 2024 جميع الحقوق محفوظة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showTermsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الشروط والأحكام'),
        content: const Text('اقرأ شروط وأحكام استخدام التطبيق'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سياسة الخصوصية'),
        content: const Text('كيف نحمي بياناتك ونحافظ على خصوصيتك'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسجيل الخروج بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red[600]),
            ),
          ),
        ],
      ),
    );
  }
}

// صفحة انشر إعلان
class AddAdScreen extends StatefulWidget {
  const AddAdScreen({super.key});

  @override
  State<AddAdScreen> createState() => _AddAdScreenState();
}

class _AddAdScreenState extends State<AddAdScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  String _selectedCategory = 'سيارات';
  String _selectedCountry = 'السعودية';
  String _selectedCity = 'الرياض';
  final List<XFile> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();

  final List<String> _categories = [
    'مقاولات', 'إلكترونيات', 'عقارات', 'مركبات', 'أزياء و أسرة', 'حيوانات',
    'تخييم', 'خدمات', 'أغراض متنوعة', 'وظائف', 'أثاث', 'هدايا'
  ];

  final Map<String, List<String>> _gulfCountriesAndCities = {
    'السعودية': [
      'الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة',
      'الطائف', 'تبوك', 'بريدة', 'خميس مشيط', 'حائل', 'الأحساء', 'القطيف'
    ],
    'الإمارات': [
      'دبي', 'أبوظبي', 'الشارقة', 'عجمان', 'أم القيوين', 'رأس الخيمة', 'الفجيرة'
    ],
    'الكويت': [
      'مدينة الكويت', 'الأحمدي', 'حولي', 'الفروانية', 'الجهراء', 'مبارك الكبير'
    ],
    'قطر': [
      'الدوحة', 'الريان', 'الوكرة', 'أم صلال', 'الخور', 'الشمال', 'الضعاين'
    ],
    'البحرين': [
      'المنامة', 'المحرق', 'الرفاع', 'مدينة حمد', 'مدينة عيسى', 'سترة', 'جدحفص'
    ],
    'عُمان': [
      'مسقط', 'صلالة', 'نزوى', 'صور', 'صحار', 'الرستاق', 'إبراء', 'البريمي'
    ],
  };

  List<String> get _availableCities => _gulfCountriesAndCities[_selectedCountry] ?? [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('انشر إعلان جديد'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الصفحة
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.teal, Colors.tealAccent],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Column(
                  children: [
                    Icon(
                      Icons.add_circle_outline,
                      size: 48,
                      color: Colors.white,
                    ),
                    SizedBox(height: 12),
                    Text(
                      'انشر إعلانك مجاناً',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'وصل لآلاف المشترين في جميع أنحاء الخليج',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // عنوان الإعلان
              _buildSectionTitle('عنوان الإعلان'),
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  hintText: 'اكتب عنوان جذاب لإعلانك',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عنوان الإعلان';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // الفئة
              _buildSectionTitle('الفئة'),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: _categories.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
              ),

              const SizedBox(height: 16),

              // الوصف
              _buildSectionTitle('الوصف'),
              TextFormField(
                controller: _descriptionController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: 'اكتب وصف مفصل للمنتج أو الخدمة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال وصف الإعلان';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // السعر
              _buildSectionTitle('السعر'),
              TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  hintText: 'السعر بالريال السعودي',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  suffixText: 'ريال',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال السعر';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // الدولة
              _buildSectionTitle('الدولة'),
              DropdownButtonFormField<String>(
                value: _selectedCountry,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.flag),
                ),
                items: _gulfCountriesAndCities.keys.map((country) {
                  return DropdownMenuItem(
                    value: country,
                    child: Text(country),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCountry = value!;
                    _selectedCity = _availableCities.first;
                  });
                },
              ),

              const SizedBox(height: 16),

              // المدينة
              _buildSectionTitle('المدينة'),
              DropdownButtonFormField<String>(
                value: _selectedCity,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_city),
                ),
                items: _availableCities.map((city) {
                  return DropdownMenuItem(
                    value: city,
                    child: Text(city),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCity = value!;
                  });
                },
              ),

              const SizedBox(height: 24),

              // قسم الصور
              _buildSectionTitle('الصور (${_selectedImages.length}/10)'),

              // عرض الصور المختارة
              if (_selectedImages.isNotEmpty) ...[
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _selectedImages.length,
                    itemBuilder: (context, index) {
                      return Container(
                        width: 100,
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                _selectedImages[index].path,
                                width: 100,
                                height: 120,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 100,
                                    height: 120,
                                    color: Colors.grey[200],
                                    child: const Icon(
                                      Icons.image,
                                      color: Colors.grey,
                                      size: 40,
                                    ),
                                  );
                                },
                              ),
                            ),
                            Positioned(
                              top: 4,
                              right: 4,
                              child: GestureDetector(
                                onTap: () => _removeImage(index),
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 12),
              ],

              // أزرار إضافة الصور
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _selectedImages.length < 10 ? () => _pickImages(ImageSource.gallery) : null,
                      icon: const Icon(Icons.photo_library),
                      label: const Text('من المعرض'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _selectedImages.length < 10 ? () => _pickImages(ImageSource.camera) : null,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('التقط صورة'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // معلومات الصور
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'أضف صور واضحة وجذابة لمنتجك. الصورة الأولى ستكون الصورة الرئيسية.',
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // أزرار العمل
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _clearForm,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: Colors.grey),
                      ),
                      child: const Text(
                        'مسح الكل',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          _publishAd();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'نشر الإعلان',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'نصائح لإعلان ناجح',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• اكتب عنوان واضح ومختصر\n'
                      '• أضف وصف مفصل وصادق\n'
                      '• ضع سعر مناسب وواقعي\n'
                      '• أضف صور عالية الجودة\n'
                      '• اختر الفئة والمدينة الصحيحة',
                      style: TextStyle(
                        color: Colors.blue,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  // دوال معالجة الصور
  Future<void> _pickImages(ImageSource source) async {
    try {
      if (source == ImageSource.gallery) {
        // اختيار صورة واحدة من المعرض (يمكن تكرار العملية)
        final XFile? image = await _picker.pickImage(source: source);
        if (image != null && _selectedImages.length < 10) {
          setState(() {
            _selectedImages.add(image);
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة الصورة'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } else {
        // التقاط صورة من الكاميرا
        final XFile? image = await _picker.pickImage(source: source);
        if (image != null && _selectedImages.length < 10) {
          setState(() {
            _selectedImages.add(image);
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة الصورة'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حذف الصورة'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    setState(() {
      _selectedCategory = 'سيارات';
      _selectedCountry = 'السعودية';
      _selectedCity = 'الرياض';
      _selectedImages.clear();
    });
  }

  void _publishAd() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تم نشر الإعلان بنجاح!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العنوان: ${_titleController.text}'),
            Text('الفئة: $_selectedCategory'),
            Text('السعر: ${_priceController.text} ريال'),
            Text('الدولة: $_selectedCountry'),
            Text('المدينة: $_selectedCity'),
            Text('عدد الصور: ${_selectedImages.length}'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم مراجعة إعلانك وسيظهر خلال 24 ساعة في جميع أنحاء الخليج',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearForm();
            },
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }
}

// صفحة الفئة
class CategoryPage extends StatelessWidget {
  final String categoryName;
  final IconData categoryIcon;
  final Color categoryColor;

  const CategoryPage({
    super.key,
    required this.categoryName,
    required this.categoryIcon,
    required this.categoryColor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(categoryName),
        backgroundColor: categoryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الفئة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: categoryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: categoryColor.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: categoryColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      categoryIcon,
                      size: 48,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    categoryName,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: categoryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اكتشف أفضل المنتجات في فئة $categoryName',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // الفئات الفرعية (للإلكترونيات فقط)
            if (categoryName == 'إلكترونيات') ...[
              const Text(
                'أقسام الإلكترونيات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              GridView.count(
                crossAxisCount: 4,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisSpacing: 12,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
                children: [
                  _buildSubCategoryCard(context, 'موبايلات و اكسسوارات', Icons.smartphone, Colors.blue),
                  _buildSubCategoryCard(context, 'تابلت / ايباد', Icons.tablet_mac, Colors.purple),
                  _buildSubCategoryCard(context, 'كاميرات', Icons.camera_alt, Colors.green),
                  _buildSubCategoryCard(context, 'أجهزة منزلية / مكتبية', Icons.home_outlined, Colors.brown),

                  _buildSubCategoryCard(context, 'ألعاب الفيديو و ملحقاتها', Icons.sports_esports, Colors.red),
                  _buildSubCategoryCard(context, 'أرقام مميزات', Icons.confirmation_number, Colors.amber),
                  _buildSubCategoryCard(context, 'الصوت و السماعات', Icons.headphones, Colors.indigo),
                  _buildSubCategoryCard(context, 'لابتوب وكمبيوتر', Icons.computer, Colors.teal),

                  _buildSubCategoryCard(context, 'ساعات ذكية', Icons.watch, Colors.pink),
                  _buildSubCategoryCard(context, 'للتلفزيونات ذكية', Icons.tv, Colors.deepOrange),
                  _buildSubCategoryCard(context, 'ريسيفرات', Icons.router, Colors.cyan),
                  _buildSubCategoryCard(context, 'أجهزة و شبكات', Icons.network_wifi, Colors.lime),

                  _buildSubCategoryCard(context, 'أجهزة أخرى', Icons.devices_other, Colors.grey),
                  _buildSubCategoryCard(context, 'خدمات إلكترونية', Icons.build_circle, Colors.deepPurple),
                  _buildSubCategoryCard(context, 'مطلوب و بشتري', Icons.shopping_cart_outlined, Colors.orange),
                ],
              ),

              const SizedBox(height: 32),

              // زر عرض الكل
              Center(
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('عرض جميع منتجات $categoryName'),
                        backgroundColor: categoryColor,
                      ),
                    );
                  },
                  icon: const Icon(Icons.grid_view),
                  label: const Text('عرض الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: categoryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                  ),
                ),
              ),
            ] else ...[
              // قائمة المنتجات العادية للفئات الأخرى
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 10,
                itemBuilder: (context, index) {
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ListTile(
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: categoryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          categoryIcon,
                          color: categoryColor,
                        ),
                      ),
                      title: Text('منتج ${index + 1} - $categoryName'),
                      subtitle: Text('وصف المنتج ${index + 1}'),
                      trailing: Text(
                        '${(index + 1) * 50} ريال',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: categoryColor,
                        ),
                      ),
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('تم اختيار منتج ${index + 1}'),
                            backgroundColor: categoryColor,
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSubCategoryCard(BuildContext context, String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اختيار: $title'),
            backgroundColor: color,
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

// الصفحات المفقودة للتنقل
class ProductDetailsPage extends StatelessWidget {
  final String productId;
  final String productName;
  final String productPrice;
  final String productImage;
  final String productDescription;

  const ProductDetailsPage({
    super.key,
    required this.productId,
    required this.productName,
    required this.productPrice,
    required this.productImage,
    required this.productDescription,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(productName),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.image, size: 64, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Text(
              productName,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              productPrice,
              style: const TextStyle(fontSize: 20, color: Colors.teal, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              productDescription,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SearchScreen();
  }
}

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfileScreen();
  }
}

class AddProductPage extends StatelessWidget {
  const AddProductPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const AddAdScreen();
  }
}

class FavoritesPage extends StatelessWidget {
  const FavoritesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FavoritesScreen();
  }
}

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final SmartThemeService _themeService = SmartThemeService();
  final SmartLanguageService _languageService = SmartLanguageService();
  final SmartNotificationsService _notificationsService = SmartNotificationsService();
  final SmartRecommendationsService _recommendationsService = SmartRecommendationsService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_languageService.getText('settings')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _themeService,
          _languageService,
          _notificationsService,
          _recommendationsService,
        ]),
        builder: (context, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // إعدادات المظهر
              _buildSectionHeader('المظهر والعرض'),
              _buildThemeSettings(),

              const SizedBox(height: 24),

              // إعدادات اللغة
              _buildSectionHeader('اللغة والمنطقة'),
              _buildLanguageSettings(),

              const SizedBox(height: 24),

              // إعدادات الإشعارات
              _buildSectionHeader('الإشعارات الذكية'),
              _buildNotificationSettings(),

              const SizedBox(height: 24),

              // إعدادات التوصيات
              _buildSectionHeader('التوصيات والذكاء الاصطناعي'),
              _buildRecommendationSettings(),

              const SizedBox(height: 24),

              // إعدادات أخرى
              _buildSectionHeader('إعدادات أخرى'),
              _buildOtherSettings(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.teal,
        ),
      ),
    );
  }

  Widget _buildThemeSettings() {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('الوضع الليلي التلقائي'),
            subtitle: const Text('يتغير حسب إعدادات النظام'),
            value: _themeService.isAutoDarkMode,
            onChanged: (value) {
              _themeService.toggleAutoDarkMode(value);
            },
          ),
          if (!_themeService.isAutoDarkMode) ...[
            const Divider(height: 1),
            ListTile(
              title: const Text('السمة'),
              subtitle: Text(_getThemeName()),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showThemeDialog,
            ),
          ],
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('الوضع المجدول'),
            subtitle: const Text('تفعيل الوضع الليلي في أوقات محددة'),
            value: _themeService.isScheduledMode,
            onChanged: (value) {
              _themeService.toggleScheduledMode(value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSettings() {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('كشف اللغة تلقائياً'),
            subtitle: const Text('استخدام لغة النظام'),
            value: _languageService.isAutoDetectEnabled,
            onChanged: (value) {
              _languageService.toggleAutoDetect(value);
            },
          ),
          if (!_languageService.isAutoDetectEnabled) ...[
            const Divider(height: 1),
            ListTile(
              title: const Text('اللغة'),
              subtitle: Text(_languageService.currentLanguageName),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showLanguageDialog,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('الإشعارات'),
            subtitle: const Text('تفعيل/إلغاء جميع الإشعارات'),
            value: _notificationsService.notificationsEnabled,
            onChanged: (value) {
              _notificationsService.updateNotificationSettings(enabled: value);
            },
          ),
          if (_notificationsService.notificationsEnabled) ...[
            const Divider(height: 1),
            SwitchListTile(
              title: const Text('الإشعارات الذكية'),
              subtitle: const Text('إشعارات مخصصة بناءً على اهتماماتك'),
              value: _notificationsService.smartNotificationsEnabled,
              onChanged: (value) {
                _notificationsService.updateNotificationSettings(smartEnabled: value);
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendationSettings() {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('التوصيات الذكية'),
            subtitle: const Text('اقتراحات مخصصة بناءً على نشاطك'),
            value: _recommendationsService.isLearning,
            onChanged: (value) {
              _recommendationsService.toggleLearning(value);
            },
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('إعادة تعيين التوصيات'),
            subtitle: const Text('مسح بيانات التعلم وبدء من جديد'),
            trailing: const Icon(Icons.refresh),
            onTap: _showResetRecommendationsDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildOtherSettings() {
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('حول التطبيق'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go(AppRoutes.about),
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('سياسة الخصوصية'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go(AppRoutes.privacy),
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('الشروط والأحكام'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go(AppRoutes.terms),
          ),
        ],
      ),
    );
  }

  String _getThemeName() {
    switch (_themeService.themeMode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      case ThemeMode.system:
        return 'تلقائي';
    }
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر السمة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('فاتح'),
              value: ThemeMode.light,
              groupValue: _themeService.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _themeService.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('داكن'),
              value: ThemeMode.dark,
              groupValue: _themeService.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _themeService.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('تلقائي'),
              value: ThemeMode.system,
              groupValue: _themeService.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _themeService.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: SmartLanguageService.supportedLocales.length,
            itemBuilder: (context, index) {
              final locale = SmartLanguageService.supportedLocales[index];

              return RadioListTile<Locale>(
                title: Text(_languageService.getLanguageName(locale)),
                value: locale,
                groupValue: _languageService.currentLocale,
                onChanged: (value) {
                  if (value != null) {
                    _languageService.changeLanguage(value);
                    Navigator.of(context).pop();
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showResetRecommendationsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين التوصيات'),
        content: const Text(
          'سيتم مسح جميع بيانات التعلم والبدء من جديد. هل أنت متأكد؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _recommendationsService.resetRecommendations();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إعادة تعيين التوصيات')),
              );
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }
}

class Chat {
  final String id;
  final String name;
  final String lastMessage;
  final DateTime timestamp;
  final String avatar;
  final int unreadCount;

  Chat({
    required this.id,
    required this.name,
    required this.lastMessage,
    required this.timestamp,
    required this.avatar,
    required this.unreadCount,
  });
}

class ChatPage extends StatelessWidget {
  final Chat chat;

  const ChatPage({super.key, required this.chat});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(chat.name),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: const Center(
        child: Text('صفحة المحادثة', style: TextStyle(fontSize: 18)),
      ),
    );
  }
}

class ChatsListPage extends StatelessWidget {
  const ChatsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثات'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: const Center(
        child: Text('قائمة المحادثات', style: TextStyle(fontSize: 18)),
      ),
    );
  }
}

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final SmartNotificationsService _notificationsService = SmartNotificationsService();
  final SmartLanguageService _languageService = SmartLanguageService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_languageService.getText('notifications')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
        actions: [
          if (_notificationsService.notifications.isNotEmpty) ...[
            IconButton(
              icon: const Icon(Icons.done_all),
              onPressed: () {
                _notificationsService.markAllAsRead();
              },
              tooltip: 'قراءة الكل',
            ),
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: () {
                _showClearAllDialog();
              },
              tooltip: 'مسح الكل',
            ),
          ],
        ],
      ),
      body: AnimatedBuilder(
        animation: _notificationsService,
        builder: (context, child) {
          final notifications = _notificationsService.notifications;

          if (notifications.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return _buildNotificationCard(notification);
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر الإشعارات الذكية هنا',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(SmartNotification notification) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: notification.isRead ? 1 : 3,
      child: ListTile(
        leading: _buildNotificationIcon(notification),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.body),
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(notification.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'mark_read':
                _notificationsService.markAsRead(notification.id);
                break;
              case 'delete':
                _notificationsService.deleteNotification(notification.id);
                break;
            }
          },
          itemBuilder: (context) => [
            if (!notification.isRead)
              const PopupMenuItem(
                value: 'mark_read',
                child: Text('تحديد كمقروء'),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Text('حذف'),
            ),
          ],
        ),
        onTap: () {
          if (!notification.isRead) {
            _notificationsService.markAsRead(notification.id);
          }

          // تسجيل التفاعل
          _notificationsService.recordNotificationInteraction(
            notification.id,
            'tap'
          );

          // الانتقال إلى الصفحة المرتبطة
          if (notification.actionUrl != null) {
            context.go(notification.actionUrl!);
          }
        },
      ),
    );
  }

  Widget _buildNotificationIcon(SmartNotification notification) {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case NotificationType.newProduct:
        iconData = Icons.new_releases;
        iconColor = Colors.green;
        break;
      case NotificationType.priceAlert:
        iconData = Icons.price_change;
        iconColor = Colors.orange;
        break;
      case NotificationType.searchResult:
        iconData = Icons.search;
        iconColor = Colors.blue;
        break;
      case NotificationType.trending:
        iconData = Icons.trending_up;
        iconColor = Colors.purple;
        break;
      case NotificationType.recommendation:
        iconData = Icons.recommend;
        iconColor = Colors.teal;
        break;
      case NotificationType.promotion:
        iconData = Icons.local_offer;
        iconColor = Colors.red;
        break;
      case NotificationType.system:
        iconData = Icons.info;
        iconColor = Colors.grey;
        break;
      case NotificationType.chat:
        iconData = Icons.chat;
        iconColor = Colors.indigo;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withValues(alpha: 0.1),
      child: Icon(iconData, color: iconColor),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع الإشعارات'),
        content: const Text('هل أنت متأكد من مسح جميع الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _notificationsService.clearAllNotifications();
              Navigator.of(context).pop();
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }
}

class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعدة'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: const Center(
        child: Text('صفحة المساعدة', style: TextStyle(fontSize: 18)),
      ),
    );
  }
}

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حول التطبيق'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: const Center(
        child: Text('صفحة حول التطبيق', style: TextStyle(fontSize: 18)),
      ),
    );
  }
}

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('سياسة الخصوصية'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: const Center(
        child: Text('صفحة سياسة الخصوصية', style: TextStyle(fontSize: 18)),
      ),
    );
  }
}

class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الشروط والأحكام'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.home),
        ),
      ),
      body: const Center(
        child: Text('صفحة الشروط والأحكام', style: TextStyle(fontSize: 18)),
      ),
    );
  }
}
