import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../models/product_model.dart';
import '../../../../models/payment_method_model.dart';
import '../../../../services/payment_service.dart';
import '../../../../services/auth_service.dart';
import '../widgets/payment_method_card.dart';
import '../widgets/order_summary_card.dart';
import '../widgets/add_payment_method_dialog.dart';

class PaymentPage extends StatefulWidget {
  final ProductModel product;
  final int quantity;
  final double totalAmount;

  const PaymentPage({
    super.key,
    required this.product,
    required this.quantity,
    required this.totalAmount,
  });

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  PaymentMethodModel? _selectedPaymentMethod;
  bool _isProcessing = false;
  String _selectedDeliveryOption = 'standard';
  double _deliveryFee = 15.0;
  double _taxAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateTax();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPaymentMethods();
    });
  }

  void _calculateTax() {
    _taxAmount = widget.totalAmount * 0.15; // 15% VAT
  }

  void _loadPaymentMethods() {
    final paymentService = Provider.of<PaymentService>(context, listen: false);
    paymentService.loadUserPaymentMethods();
  }

  double get _finalAmount => widget.totalAmount + _deliveryFee + _taxAmount;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('الدفع'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order Summary
                  OrderSummaryCard(
                    product: widget.product,
                    quantity: widget.quantity,
                    subtotal: widget.totalAmount,
                    deliveryFee: _deliveryFee,
                    taxAmount: _taxAmount,
                    total: _finalAmount,
                  ),

                  const SizedBox(height: 24),

                  // Delivery Options
                  _buildDeliveryOptions(),

                  const SizedBox(height: 24),

                  // Payment Methods
                  _buildPaymentMethods(),

                  const SizedBox(height: 24),

                  // Security Notice
                  _buildSecurityNotice(),

                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),

          // Payment Button
          _buildPaymentButton(),
        ],
      ),
    );
  }

  Widget _buildDeliveryOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'خيارات التوصيل',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            RadioListTile<String>(
              title: const Text('التوصيل العادي'),
              subtitle: const Text('3-5 أيام عمل - 15 ريال'),
              value: 'standard',
              groupValue: _selectedDeliveryOption,
              onChanged: (value) {
                setState(() {
                  _selectedDeliveryOption = value!;
                  _deliveryFee = 15.0;
                });
              },
              activeColor: AppColors.primary,
            ),
            
            RadioListTile<String>(
              title: const Text('التوصيل السريع'),
              subtitle: const Text('1-2 أيام عمل - 30 ريال'),
              value: 'express',
              groupValue: _selectedDeliveryOption,
              onChanged: (value) {
                setState(() {
                  _selectedDeliveryOption = value!;
                  _deliveryFee = 30.0;
                });
              },
              activeColor: AppColors.primary,
            ),
            
            RadioListTile<String>(
              title: const Text('التوصيل في نفس اليوم'),
              subtitle: const Text('خلال 24 ساعة - 50 ريال'),
              value: 'same_day',
              groupValue: _selectedDeliveryOption,
              onChanged: (value) {
                setState(() {
                  _selectedDeliveryOption = value!;
                  _deliveryFee = 50.0;
                });
              },
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'طرق الدفع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: _showAddPaymentMethodDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة طريقة دفع'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        Consumer<PaymentService>(
          builder: (context, paymentService, child) {
            final paymentMethods = paymentService.userPaymentMethods;
            
            if (paymentMethods.isEmpty) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(
                        Icons.payment,
                        size: 64,
                        color: AppColors.textLight,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'لا توجد طرق دفع محفوظة',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _showAddPaymentMethodDialog,
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة طريقة دفع'),
                      ),
                    ],
                  ),
                ),
              );
            }
            
            return Column(
              children: [
                ...paymentMethods.map((method) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: PaymentMethodCard(
                      paymentMethod: method,
                      isSelected: _selectedPaymentMethod?.id == method.id,
                      onSelected: () {
                        setState(() {
                          _selectedPaymentMethod = method;
                        });
                      },
                      onEdit: () => _editPaymentMethod(method),
                      onDelete: () => _deletePaymentMethod(method),
                    ),
                  );
                }),
                
                // Cash on Delivery Option
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: PaymentMethodCard(
                    paymentMethod: PaymentMethodModel(
                      id: 'cod',
                      type: PaymentMethodType.cashOnDelivery,
                      displayName: 'الدفع عند الاستلام',
                      isDefault: false,
                    ),
                    isSelected: _selectedPaymentMethod?.id == 'cod',
                    onSelected: () {
                      setState(() {
                        _selectedPaymentMethod = PaymentMethodModel(
                          id: 'cod',
                          type: PaymentMethodType.cashOnDelivery,
                          displayName: 'الدفع عند الاستلام',
                          isDefault: false,
                        );
                      });
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildSecurityNotice() {
    return Card(
      color: AppColors.success.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.security,
              color: AppColors.success,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'دفع آمن ومحمي',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'جميع المعاملات محمية بتشفير SSL وتتم معالجتها بأمان',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentButton() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(color: AppColors.border),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المجموع الكلي:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${_finalAmount.toStringAsFixed(2)} ريال',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _selectedPaymentMethod != null && !_isProcessing
                  ? _processPayment
                  : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isProcessing
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _selectedPaymentMethod?.type == PaymentMethodType.cashOnDelivery
                          ? 'تأكيد الطلب'
                          : 'ادفع الآن',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddPaymentMethodDialog() {
    showDialog(
      context: context,
      builder: (context) => AddPaymentMethodDialog(
        onPaymentMethodAdded: () {
          _loadPaymentMethods();
        },
      ),
    );
  }

  void _editPaymentMethod(PaymentMethodModel method) {
    // Navigate to edit payment method page
  }

  void _deletePaymentMethod(PaymentMethodModel method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف طريقة الدفع'),
        content: Text('هل تريد حذف ${method.displayName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final paymentService = Provider.of<PaymentService>(context, listen: false);
              paymentService.deletePaymentMethod(method.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final paymentService = Provider.of<PaymentService>(context, listen: false);
      final authService = Provider.of<AuthService>(context, listen: false);

      final success = await paymentService.processPayment(
        amount: _finalAmount,
        paymentMethod: _selectedPaymentMethod!,
        productId: widget.product.id,
        quantity: widget.quantity,
        userId: authService.currentUser!.id,
      );

      if (success && mounted) {
        Navigator.pushReplacementNamed(
          context,
          '/payment-success',
          arguments: {
            'orderId': DateTime.now().millisecondsSinceEpoch.toString(),
            'amount': _finalAmount,
            'product': widget.product,
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في معالجة الدفع: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
