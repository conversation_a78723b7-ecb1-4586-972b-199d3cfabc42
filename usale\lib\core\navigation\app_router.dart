import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../main_final.dart';

/// مسارات التطبيق
class AppRoutes {
  static const String home = '/';
  static const String category = '/category';
  static const String productDetails = '/product-details';
  static const String addProduct = '/add-product';
  static const String search = '/search';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String chat = '/chat';
  static const String chatsList = '/chats-list';
  static const String favorites = '/favorites';
  static const String notifications = '/notifications';
  static const String help = '/help';
  static const String about = '/about';
  static const String privacy = '/privacy';
  static const String terms = '/terms';
}

/// تكوين التنقل الرئيسي للتطبيق
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.home,
    debugLogDiagnostics: true,
    routes: [
      // الصفحة الرئيسية
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const USaleHomePage(),
        ),
      ),

      // صفحة الفئة
      GoRoute(
        path: AppRoutes.category,
        name: 'category',
        pageBuilder: (context, state) {
          final categoryName = state.uri.queryParameters['name'] ?? '';
          // final iconCodePoint = int.tryParse(state.uri.queryParameters['icon'] ?? '0') ?? 0; // Not used
          final colorValue = int.tryParse(state.uri.queryParameters['color'] ?? '0') ?? 0;

          // استخدام أيقونة افتراضية إذا لم يتم العثور على الأيقونة
          const IconData categoryIcon = Icons.category;

          return _buildPageWithTransition(
            context: context,
            state: state,
            child: CategoryPage(
              categoryName: categoryName,
              categoryIcon: categoryIcon,
              categoryColor: Color(colorValue),
            ),
          );
        },
      ),

      // صفحة تفاصيل المنتج
      GoRoute(
        path: AppRoutes.productDetails,
        name: 'productDetails',
        pageBuilder: (context, state) {
          final productId = state.uri.queryParameters['id'] ?? '';
          final productName = state.uri.queryParameters['name'] ?? '';
          final productPrice = state.uri.queryParameters['price'] ?? '';
          final productImage = state.uri.queryParameters['image'] ?? '';
          final productDescription = state.uri.queryParameters['description'] ?? '';
          
          return _buildPageWithTransition(
            context: context,
            state: state,
            child: ProductDetailsPage(
              productId: productId,
              productName: productName,
              productPrice: productPrice,
              productImage: productImage,
              productDescription: productDescription,
            ),
          );
        },
      ),

      // صفحة إضافة منتج
      GoRoute(
        path: AppRoutes.addProduct,
        name: 'addProduct',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const AddProductPage(),
          transitionType: PageTransitionType.slideFromBottom,
        ),
      ),

      // صفحة البحث
      GoRoute(
        path: AppRoutes.search,
        name: 'search',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const SearchPage(),
          transitionType: PageTransitionType.fade,
        ),
      ),

      // صفحة الملف الشخصي
      GoRoute(
        path: AppRoutes.profile,
        name: 'profile',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const ProfilePage(),
        ),
      ),

      // صفحة الإعدادات
      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const SettingsPage(),
        ),
      ),

      // صفحة المحادثة
      GoRoute(
        path: AppRoutes.chat,
        name: 'chat',
        pageBuilder: (context, state) {
          final chatId = state.uri.queryParameters['id'] ?? '';
          final chatName = state.uri.queryParameters['name'] ?? '';
          final chatAvatar = state.uri.queryParameters['avatar'] ?? '';
          
          return _buildPageWithTransition(
            context: context,
            state: state,
            child: ChatPage(
              chat: Chat(
                id: chatId,
                name: chatName,
                lastMessage: '',
                timestamp: DateTime.now(),
                avatar: chatAvatar,
                unreadCount: 0,
              ),
            ),
          );
        },
      ),

      // صفحة قائمة المحادثات
      GoRoute(
        path: AppRoutes.chatsList,
        name: 'chatsList',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const ChatsListPage(),
        ),
      ),

      // صفحة المفضلة
      GoRoute(
        path: AppRoutes.favorites,
        name: 'favorites',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const FavoritesPage(),
        ),
      ),

      // صفحة الإشعارات
      GoRoute(
        path: AppRoutes.notifications,
        name: 'notifications',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const NotificationsPage(),
        ),
      ),

      // صفحة المساعدة
      GoRoute(
        path: AppRoutes.help,
        name: 'help',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const HelpPage(),
        ),
      ),

      // صفحة حول التطبيق
      GoRoute(
        path: AppRoutes.about,
        name: 'about',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const AboutPage(),
        ),
      ),

      // صفحة سياسة الخصوصية
      GoRoute(
        path: AppRoutes.privacy,
        name: 'privacy',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const PrivacyPolicyPage(),
        ),
      ),

      // صفحة الشروط والأحكام
      GoRoute(
        path: AppRoutes.terms,
        name: 'terms',
        pageBuilder: (context, state) => _buildPageWithTransition(
          context: context,
          state: state,
          child: const TermsOfServicePage(),
        ),
      ),
    ],
    
    // معالج الأخطاء
    errorPageBuilder: (context, state) => MaterialPage<void>(
      key: state.pageKey,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('خطأ'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'عذراً، حدث خطأ!',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'الصفحة المطلوبة غير موجودة',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go(AppRoutes.home),
                child: const Text('العودة للرئيسية'),
              ),
            ],
          ),
        ),
      ),
    ),
  );

  /// بناء صفحة مع انتقال مخصص
  static Page<dynamic> _buildPageWithTransition({
    required BuildContext context,
    required GoRouterState state,
    required Widget child,
    Duration duration = const Duration(milliseconds: 300),
    PageTransitionType transitionType = PageTransitionType.slideFromRight,
  }) {
    return CustomTransitionPage<void>(
      key: state.pageKey,
      child: child,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return _buildTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
          transitionType: transitionType,
        );
      },
    );
  }

  /// بناء انتقالات مختلفة
  static Widget _buildTransition({
    required Animation<double> animation,
    required Animation<double> secondaryAnimation,
    required Widget child,
    required PageTransitionType transitionType,
  }) {
    switch (transitionType) {
      case PageTransitionType.slideFromRight:
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );

      case PageTransitionType.slideFromLeft:
        const begin = Offset(-1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );

      case PageTransitionType.slideFromBottom:
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );

      case PageTransitionType.fade:
        return FadeTransition(
          opacity: animation,
          child: child,
        );

      case PageTransitionType.scale:
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            ),
          ),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );

      case PageTransitionType.rotation:
        return RotationTransition(
          turns: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            ),
          ),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
    }
  }
}

/// أنواع انتقالات الصفحات
enum PageTransitionType {
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  fade,
  scale,
  rotation,
}

/// امتدادات مساعدة للتنقل
extension AppRouterExtension on BuildContext {
  /// الانتقال إلى صفحة الفئة
  void goToCategory(String categoryName, IconData icon, Color color) {
    final colorValue = color.value; // Using deprecated value for compatibility
    go('${AppRoutes.category}?name=${Uri.encodeComponent(categoryName)}&icon=${icon.codePoint}&color=0x${colorValue.toRadixString(16).padLeft(8, '0')}');
  }

  /// الانتقال إلى تفاصيل المنتج
  void goToProductDetails({
    required String id,
    required String name,
    required String price,
    required String image,
    required String description,
  }) {
    go('${AppRoutes.productDetails}?id=${Uri.encodeComponent(id)}&name=${Uri.encodeComponent(name)}&price=${Uri.encodeComponent(price)}&image=${Uri.encodeComponent(image)}&description=${Uri.encodeComponent(description)}');
  }

  /// الانتقال إلى المحادثة
  void goToChat(String chatId, String chatName, String chatAvatar) {
    go('${AppRoutes.chat}?id=${Uri.encodeComponent(chatId)}&name=${Uri.encodeComponent(chatName)}&avatar=${Uri.encodeComponent(chatAvatar)}');
  }
}
