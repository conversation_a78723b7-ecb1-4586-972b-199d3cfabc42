enum PaymentMethodType {
  creditCard,
  debitCard,
  applePay,
  googlePay,
  paypal,
  bankTransfer,
  wallet,
}

enum TransactionType {
  payment,
  refund,
  walletTopup,
  walletPayment,
  withdrawal,
  commission,
}

enum TransactionStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

class PaymentMethodModel {
  final String id;
  final String userId;
  final PaymentMethodType type;
  final String cardNumber;
  final String expiryDate;
  final String holderName;
  final String? bankName;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PaymentMethodModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.cardNumber,
    required this.expiryDate,
    required this.holderName,
    this.bankName,
    this.isDefault = false,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      type: PaymentMethodType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'creditCard'),
        orElse: () => PaymentMethodType.creditCard,
      ),
      cardNumber: json['card_number'] ?? '',
      expiryDate: json['expiry_date'] ?? '',
      holderName: json['holder_name'] ?? '',
      bankName: json['bank_name'],
      isDefault: json['is_default'] ?? false,
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.toString().split('.').last,
      'card_number': cardNumber,
      'expiry_date': expiryDate,
      'holder_name': holderName,
      'bank_name': bankName,
      'is_default': isDefault,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  PaymentMethodModel copyWith({
    String? id,
    String? userId,
    PaymentMethodType? type,
    String? cardNumber,
    String? expiryDate,
    String? holderName,
    String? bankName,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethodModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      cardNumber: cardNumber ?? this.cardNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      holderName: holderName ?? this.holderName,
      bankName: bankName ?? this.bankName,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName {
    switch (type) {
      case PaymentMethodType.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethodType.debitCard:
        return 'بطاقة خصم';
      case PaymentMethodType.applePay:
        return 'Apple Pay';
      case PaymentMethodType.googlePay:
        return 'Google Pay';
      case PaymentMethodType.paypal:
        return 'PayPal';
      case PaymentMethodType.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethodType.wallet:
        return 'المحفظة';
    }
  }

  @override
  String toString() {
    return 'PaymentMethodModel(id: $id, type: $type, cardNumber: $cardNumber)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethodModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class TransactionModel {
  final String id;
  final String userId;
  final TransactionType type;
  final double amount;
  final String currency;
  final TransactionStatus status;
  final String description;
  final String? paymentMethodId;
  final String? orderId;
  final String? productId;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  TransactionModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.currency,
    required this.status,
    required this.description,
    this.paymentMethodId,
    this.orderId,
    this.productId,
    this.metadata = const {},
    required this.createdAt,
    this.updatedAt,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      type: TransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'payment'),
        orElse: () => TransactionType.payment,
      ),
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'SAR',
      status: TransactionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == (json['status'] ?? 'pending'),
        orElse: () => TransactionStatus.pending,
      ),
      description: json['description'] ?? '',
      paymentMethodId: json['payment_method_id'],
      orderId: json['order_id'],
      productId: json['product_id'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.toString().split('.').last,
      'amount': amount,
      'currency': currency,
      'status': status.toString().split('.').last,
      'description': description,
      'payment_method_id': paymentMethodId,
      'order_id': orderId,
      'product_id': productId,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  TransactionModel copyWith({
    String? id,
    String? userId,
    TransactionType? type,
    double? amount,
    String? currency,
    TransactionStatus? status,
    String? description,
    String? paymentMethodId,
    String? orderId,
    String? productId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      description: description ?? this.description,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get formattedAmount {
    final sign = amount >= 0 ? '+' : '';
    return '$sign${amount.toStringAsFixed(2)} $currency';
  }

  String get statusText {
    switch (status) {
      case TransactionStatus.pending:
        return 'قيد الانتظار';
      case TransactionStatus.processing:
        return 'قيد المعالجة';
      case TransactionStatus.completed:
        return 'مكتملة';
      case TransactionStatus.failed:
        return 'فشلت';
      case TransactionStatus.cancelled:
        return 'ملغية';
      case TransactionStatus.refunded:
        return 'مستردة';
    }
  }

  String get typeText {
    switch (type) {
      case TransactionType.payment:
        return 'دفع';
      case TransactionType.refund:
        return 'استرداد';
      case TransactionType.walletTopup:
        return 'شحن المحفظة';
      case TransactionType.walletPayment:
        return 'دفع من المحفظة';
      case TransactionType.withdrawal:
        return 'سحب';
      case TransactionType.commission:
        return 'عمولة';
    }
  }

  @override
  String toString() {
    return 'TransactionModel(id: $id, type: $type, amount: $amount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class WalletModel {
  final String id;
  final String userId;
  final double balance;
  final String currency;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  WalletModel({
    required this.id,
    required this.userId,
    required this.balance,
    required this.currency,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      balance: (json['balance'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'SAR',
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'balance': balance,
      'currency': currency,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  WalletModel copyWith({
    String? id,
    String? userId,
    double? balance,
    String? currency,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WalletModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get formattedBalance {
    return '${balance.toStringAsFixed(2)} $currency';
  }

  @override
  String toString() {
    return 'WalletModel(id: $id, balance: $balance, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WalletModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
