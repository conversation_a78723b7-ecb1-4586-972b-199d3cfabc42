rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      // Users can read and write their own data
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow reading basic user info for other users (for chat, reviews, etc.)
      allow read: if request.auth != null && 
        resource.data.keys().hasOnly(['name', 'profile_image', 'user_type', 'created_at']);
    }
    
    // Products collection
    match /products/{productId} {
      // Anyone can read products
      allow read: if true;
      
      // Only authenticated users can create products
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.seller_id;
      
      // Only product owner can update/delete
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.seller_id;
    }
    
    // Categories collection
    match /categories/{categoryId} {
      // Anyone can read categories
      allow read: if true;
      
      // Only admins can write categories (implement admin check)
      allow write: if false; // For now, disable writes
    }
    
    // Chats collection
    match /chats/{chatId} {
      // Only participants can read/write chat
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      
      // Allow creating new chats
      allow create: if request.auth != null && 
        request.auth.uid in request.resource.data.participants;
    }
    
    // Messages subcollection
    match /chats/{chatId}/messages/{messageId} {
      // Only chat participants can read messages
      allow read: if request.auth != null && 
        request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
      
      // Only authenticated users can send messages
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.sender_id &&
        request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
      
      // Only message sender can update/delete
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.sender_id;
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      // Anyone can read reviews
      allow read: if true;
      
      // Only authenticated users can create reviews
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.reviewer_id;
      
      // Only review author can update/delete
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.reviewer_id;
    }
    
    // Favorites collection
    match /favorites/{favoriteId} {
      // Only owner can read/write favorites
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      
      // Allow creating favorites
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.user_id;
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      // Only notification recipient can read
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      
      // Only recipient can update (mark as read)
      allow update: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      
      // System can create notifications
      allow create: if true; // In real app, restrict to server
    }
    
    // Transactions collection
    match /transactions/{transactionId} {
      // Only transaction participants can read
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.buyer_id || 
         request.auth.uid == resource.data.seller_id);
      
      // Only buyer can create transactions
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.buyer_id;
      
      // Only participants can update transaction status
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.buyer_id || 
         request.auth.uid == resource.data.seller_id);
    }
    
    // Reports collection
    match /reports/{reportId} {
      // Only report creator can read their reports
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.reporter_id;
      
      // Authenticated users can create reports
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.reporter_id;
    }
    
    // Analytics collection (read-only for users)
    match /analytics/{document=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only server can write analytics
    }
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }
  }
}
