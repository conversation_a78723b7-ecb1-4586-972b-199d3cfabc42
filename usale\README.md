# يو سيل (USale) - تطبيق السوق الإلكتروني

تطبيق سوق إلكتروني شامل مطور بـ Flutter يوفر تجربة مستخدم متميزة للبيع والشراء عبر الإنترنت.

## 🌟 الميزات الرئيسية

### 🏠 الصفحة الرئيسية
- واجهة مستخدم جذابة ومتجاوبة
- عرض الفئات الرئيسية بتصميم أنيق
- قسم المنتجات المميزة
- قسم أحدث المنتجات
- شريط بحث متقدم مع فلاتر

### 🔍 نظام البحث المتقدم
- بحث نصي ذكي
- فلترة حسب الفئة والحالة والموقع والسعر
- ترتيب النتائج بطرق متعددة
- حفظ تاريخ البحث
- اقتراحات البحث

### ❤️ نظام المفضلة
- إضافة وإزالة المنتجات من المفضلة
- تنظيم المفضلة في مجموعات
- مزامنة المفضلة عبر الأجهزة
- بحث في المفضلة

### 💬 نظام المراسلة
- دردشة فورية بين المستخدمين
- إرسال الصور والملفات
- إشعارات الرسائل الجديدة
- حالة الاتصال (متصل/غير متصل)

### 📱 إدارة المنتجات
- إضافة منتجات جديدة بسهولة
- تحرير وحذف المنتجات
- رفع صور متعددة
- تتبع المشاهدات والإعجابات
- إدارة حالة المنتج (متاح/مباع/محجوز)

### 👤 الملف الشخصي
- تحرير المعلومات الشخصية
- إعدادات التطبيق المتقدمة
- إدارة الخصوصية والأمان
- تصدير البيانات

### 🔔 نظام الإشعارات
- إشعارات الرسائل الجديدة
- تنبيهات المنتجات المفضلة
- إشعارات البيع والشراء
- إشعارات العروض والخصومات

## 🛠️ التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **Material Design 3**: نظام التصميم
- **SharedPreferences**: تخزين البيانات المحلية
- **Image Picker**: اختيار الصور
- **Share Plus**: مشاركة المحتوى

## 📱 المتطلبات

- Flutter SDK 3.0.0 أو أحدث
- Dart SDK 2.17.0 أو أحدث
- Android Studio / VS Code
- Android SDK (للأندرويد)
- Xcode (لـ iOS)

## 🚀 التثبيت والتشغيل

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/usale.git
cd usale
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

## 🏗️ هيكل المشروع

```
lib/
├── core/                    # الملفات الأساسية
│   ├── constants/          # الثوابت
│   ├── theme/              # التصميم والألوان
│   └── utils/              # الأدوات المساعدة
├── features/               # الميزات الرئيسية
│   ├── home/              # الصفحة الرئيسية
│   ├── search/            # البحث
│   ├── favorites/         # المفضلة
│   ├── chat/              # المراسلة
│   ├── products/          # إدارة المنتجات
│   ├── profile/           # الملف الشخصي
│   └── notifications/     # الإشعارات
├── models/                # نماذج البيانات
├── services/              # الخدمات
└── main.dart             # نقطة البداية
```

## 🎨 التصميم

التطبيق يستخدم Material Design 3 مع:
- نظام ألوان متناسق
- خطوط عربية واضحة
- رسوم متحركة سلسة
- تصميم متجاوب لجميع الأحجام

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/widget_test.dart
```

## 📈 الأداء

التطبيق محسن للأداء من خلال:
- تحميل البيانات بشكل تدريجي (Lazy Loading)
- تخزين مؤقت للصور
- تحسين قوائم العرض
- إدارة ذكية للذاكرة

## 🌐 الدعم اللغوي

- العربية (افتراضي)
- الإنجليزية

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📞 التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://usale.com
- تويتر: [@usale_app](https://twitter.com/usale_app)

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا التطبيق.

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير ويتم تحديثه باستمرار لإضافة ميزات جديدة وتحسين الأداء.
