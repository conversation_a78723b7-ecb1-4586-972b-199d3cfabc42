enum SecurityEventType {
  loginSuccess,
  loginFailure,
  logout,
  passwordChange,
  emailChange,
  profileUpdate,
  twoFactorEnabled,
  twoFactorDisabled,
  suspiciousActivity,
  bruteForceDetected,
  accountLocked,
  accountUnlocked,
  dataExport,
  dataDelete,
  permissionChange,
}

enum PasswordStrengthLevel {
  veryWeak,
  weak,
  medium,
  strong,
}

class SecurityLog {
  final String id;
  final String? userId;
  final SecurityEventType eventType;
  final String description;
  final String? ipAddress;
  final String? userAgent;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;

  SecurityLog({
    required this.id,
    this.userId,
    required this.eventType,
    required this.description,
    this.ipAddress,
    this.userAgent,
    this.metadata = const {},
    required this.timestamp,
  });

  factory SecurityLog.fromJson(Map<String, dynamic> json) {
    return SecurityLog(
      id: json['id'] ?? '',
      userId: json['user_id'],
      eventType: SecurityEventType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['event_type'] ?? 'loginSuccess'),
        orElse: () => SecurityEventType.loginSuccess,
      ),
      description: json['description'] ?? '',
      ipAddress: json['ip_address'],
      userAgent: json['user_agent'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'event_type': eventType.toString().split('.').last,
      'description': description,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  String get eventTypeText {
    switch (eventType) {
      case SecurityEventType.loginSuccess:
        return 'تسجيل دخول ناجح';
      case SecurityEventType.loginFailure:
        return 'فشل في تسجيل الدخول';
      case SecurityEventType.logout:
        return 'تسجيل خروج';
      case SecurityEventType.passwordChange:
        return 'تغيير كلمة المرور';
      case SecurityEventType.emailChange:
        return 'تغيير البريد الإلكتروني';
      case SecurityEventType.profileUpdate:
        return 'تحديث الملف الشخصي';
      case SecurityEventType.twoFactorEnabled:
        return 'تفعيل المصادقة الثنائية';
      case SecurityEventType.twoFactorDisabled:
        return 'إلغاء المصادقة الثنائية';
      case SecurityEventType.suspiciousActivity:
        return 'نشاط مشبوه';
      case SecurityEventType.bruteForceDetected:
        return 'محاولة هجوم القوة الغاشمة';
      case SecurityEventType.accountLocked:
        return 'قفل الحساب';
      case SecurityEventType.accountUnlocked:
        return 'إلغاء قفل الحساب';
      case SecurityEventType.dataExport:
        return 'تصدير البيانات';
      case SecurityEventType.dataDelete:
        return 'حذف البيانات';
      case SecurityEventType.permissionChange:
        return 'تغيير الصلاحيات';
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) return 'الآن';
    if (difference.inMinutes < 60) return 'منذ ${difference.inMinutes} دقيقة';
    if (difference.inHours < 24) return 'منذ ${difference.inHours} ساعة';
    if (difference.inDays < 7) return 'منذ ${difference.inDays} يوم';
    return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
  }

  @override
  String toString() {
    return 'SecurityLog(eventType: $eventType, description: $description, timestamp: $timestamp)';
  }
}

class LoginAttempt {
  final String id;
  final String email;
  final bool isSuccessful;
  final String? failureReason;
  final String? ipAddress;
  final String? userAgent;
  final DateTime timestamp;

  LoginAttempt({
    required this.id,
    required this.email,
    required this.isSuccessful,
    this.failureReason,
    this.ipAddress,
    this.userAgent,
    required this.timestamp,
  });

  factory LoginAttempt.fromJson(Map<String, dynamic> json) {
    return LoginAttempt(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      isSuccessful: json['is_successful'] ?? false,
      failureReason: json['failure_reason'],
      ipAddress: json['ip_address'],
      userAgent: json['user_agent'],
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'is_successful': isSuccessful,
      'failure_reason': failureReason,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  String get statusText {
    return isSuccessful ? 'نجح' : 'فشل';
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) return 'الآن';
    if (difference.inMinutes < 60) return 'منذ ${difference.inMinutes} دقيقة';
    if (difference.inHours < 24) return 'منذ ${difference.inHours} ساعة';
    if (difference.inDays < 7) return 'منذ ${difference.inDays} يوم';
    return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
  }

  @override
  String toString() {
    return 'LoginAttempt(email: $email, isSuccessful: $isSuccessful, timestamp: $timestamp)';
  }
}

class SecuritySettings {
  final bool twoFactorEnabled;
  final String? twoFactorSecret;
  final List<String> backupCodes;
  final Duration sessionTimeout;
  final int passwordExpiryDays;
  final int maxLoginAttempts;
  final bool requireStrongPassword;
  final bool emailNotifications;
  final bool smsNotifications;
  final List<String> trustedDevices;

  SecuritySettings({
    this.twoFactorEnabled = false,
    this.twoFactorSecret,
    this.backupCodes = const [],
    this.sessionTimeout = const Duration(hours: 24),
    this.passwordExpiryDays = 90,
    this.maxLoginAttempts = 5,
    this.requireStrongPassword = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.trustedDevices = const [],
  });

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      twoFactorEnabled: json['two_factor_enabled'] ?? false,
      twoFactorSecret: json['two_factor_secret'],
      backupCodes: List<String>.from(json['backup_codes'] ?? []),
      sessionTimeout: Duration(
        milliseconds: json['session_timeout_ms'] ?? const Duration(hours: 24).inMilliseconds,
      ),
      passwordExpiryDays: json['password_expiry_days'] ?? 90,
      maxLoginAttempts: json['max_login_attempts'] ?? 5,
      requireStrongPassword: json['require_strong_password'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      smsNotifications: json['sms_notifications'] ?? false,
      trustedDevices: List<String>.from(json['trusted_devices'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'two_factor_enabled': twoFactorEnabled,
      'two_factor_secret': twoFactorSecret,
      'backup_codes': backupCodes,
      'session_timeout_ms': sessionTimeout.inMilliseconds,
      'password_expiry_days': passwordExpiryDays,
      'max_login_attempts': maxLoginAttempts,
      'require_strong_password': requireStrongPassword,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'trusted_devices': trustedDevices,
    };
  }

  SecuritySettings copyWith({
    bool? twoFactorEnabled,
    String? twoFactorSecret,
    List<String>? backupCodes,
    Duration? sessionTimeout,
    int? passwordExpiryDays,
    int? maxLoginAttempts,
    bool? requireStrongPassword,
    bool? emailNotifications,
    bool? smsNotifications,
    List<String>? trustedDevices,
  }) {
    return SecuritySettings(
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      twoFactorSecret: twoFactorSecret ?? this.twoFactorSecret,
      backupCodes: backupCodes ?? this.backupCodes,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
      passwordExpiryDays: passwordExpiryDays ?? this.passwordExpiryDays,
      maxLoginAttempts: maxLoginAttempts ?? this.maxLoginAttempts,
      requireStrongPassword: requireStrongPassword ?? this.requireStrongPassword,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      trustedDevices: trustedDevices ?? this.trustedDevices,
    );
  }

  @override
  String toString() {
    return 'SecuritySettings(twoFactorEnabled: $twoFactorEnabled, requireStrongPassword: $requireStrongPassword)';
  }
}

class PasswordStrength {
  final PasswordStrengthLevel level;
  final int score;
  final List<String> issues;

  PasswordStrength({
    required this.level,
    required this.score,
    required this.issues,
  });

  String get levelText {
    switch (level) {
      case PasswordStrengthLevel.veryWeak:
        return 'ضعيفة جداً';
      case PasswordStrengthLevel.weak:
        return 'ضعيفة';
      case PasswordStrengthLevel.medium:
        return 'متوسطة';
      case PasswordStrengthLevel.strong:
        return 'قوية';
    }
  }

  double get strengthPercentage {
    switch (level) {
      case PasswordStrengthLevel.veryWeak:
        return 0.25;
      case PasswordStrengthLevel.weak:
        return 0.5;
      case PasswordStrengthLevel.medium:
        return 0.75;
      case PasswordStrengthLevel.strong:
        return 1.0;
    }
  }

  bool get isAcceptable {
    return level == PasswordStrengthLevel.medium || level == PasswordStrengthLevel.strong;
  }

  @override
  String toString() {
    return 'PasswordStrength(level: $level, score: $score, issues: ${issues.length})';
  }
}

class TwoFactorResult {
  final bool isSuccess;
  final String message;
  final String? qrCodeData;
  final List<String>? backupCodes;

  TwoFactorResult._(this.isSuccess, this.message, [this.qrCodeData, this.backupCodes]);

  factory TwoFactorResult.success(String message, [String? qrCodeData, List<String>? backupCodes]) {
    return TwoFactorResult._(true, message, qrCodeData, backupCodes);
  }

  factory TwoFactorResult.error(String message) {
    return TwoFactorResult._(false, message);
  }
}
