class AppConstants {
  // App Info
  static const String appName = 'يو سيل';
  static const String appNameEnglish = 'USale';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق السوق الإلكتروني المتكامل';
  
  // API Configuration
  static const String baseUrl = 'https://api.usale.com';
  static const String apiVersion = 'v1';
  static const String apiKey = 'your_api_key_here';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String onboardingKey = 'onboarding_completed';
  static const String favoritesKey = 'favorites';
  static const String searchHistoryKey = 'search_history';
  static const String notificationsKey = 'notifications_enabled';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultRadius = 12.0;
  static const double smallRadius = 8.0;
  static const double largeRadius = 16.0;
  
  // Image Sizes
  static const double profileImageSize = 80.0;
  static const double productImageSize = 120.0;
  static const double categoryImageSize = 60.0;
  static const double thumbnailSize = 40.0;
  
  // Limits
  static const int maxImageUpload = 5;
  static const int maxDescriptionLength = 1000;
  static const int maxTitleLength = 100;
  static const int searchHistoryLimit = 10;
  static const int productsPerPage = 20;
  
  // Categories
  static const List<String> categories = [
    'سيارات',
    'عقارات', 
    'إلكترونيات',
    'أزياء',
    'وظائف',
    'خدمات',
    'رياضة',
    'كتب',
    'أطفال',
    'منزل وحديقة',
  ];
  
  static const List<String> categoriesEnglish = [
    'Autos',
    'Real Estate',
    'Electronics',
    'Fashion',
    'Jobs',
    'Services',
    'Sports',
    'Books',
    'Kids',
    'Home & Garden',
  ];
  
  // Product Conditions
  static const List<String> conditions = [
    'جديد',
    'مستعمل - ممتاز',
    'مستعمل - جيد',
    'مستعمل - مقبول',
  ];
  
  // Price Ranges
  static const List<String> priceRanges = [
    'أقل من 100',
    '100 - 500',
    '500 - 1000',
    '1000 - 5000',
    '5000 - 10000',
    'أكثر من 10000',
  ];
  
  // Sort Options
  static const List<String> sortOptions = [
    'الأحدث',
    'الأقدم',
    'السعر: من الأقل للأعلى',
    'السعر: من الأعلى للأقل',
    'الأكثر مشاهدة',
    'الأعلى تقييماً',
  ];
  
  // Contact Methods
  static const List<String> contactMethods = [
    'هاتف',
    'واتساب',
    'دردشة',
    'بريد إلكتروني',
  ];
  
  // Validation
  static const String phoneRegex = r'^[0-9]{10,15}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  
  // Error Messages
  static const String networkError = 'خطأ في الاتصال بالإنترنت';
  static const String serverError = 'خطأ في الخادم';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String validationError = 'يرجى التحقق من البيانات المدخلة';
  static const String authError = 'خطأ في المصادقة';
  static const String permissionError = 'ليس لديك صلاحية للوصول';
  
  // Success Messages
  static const String loginSuccess = 'تم تسجيل الدخول بنجاح';
  static const String registerSuccess = 'تم إنشاء الحساب بنجاح';
  static const String productAddedSuccess = 'تم إضافة المنتج بنجاح';
  static const String productUpdatedSuccess = 'تم تحديث المنتج بنجاح';
  static const String messageSentSuccess = 'تم إرسال الرسالة بنجاح';
  
  // Placeholder Texts
  static const String searchPlaceholder = 'ابحث عن أي شيء...';
  static const String emailPlaceholder = 'البريد الإلكتروني';
  static const String passwordPlaceholder = 'كلمة المرور';
  static const String namePlaceholder = 'الاسم الكامل';
  static const String phonePlaceholder = 'رقم الهاتف';
  static const String titlePlaceholder = 'عنوان الإعلان';
  static const String descriptionPlaceholder = 'وصف المنتج...';
  static const String pricePlaceholder = 'السعر';
  static const String locationPlaceholder = 'الموقع';
  
  // Button Texts
  static const String loginButton = 'تسجيل الدخول';
  static const String registerButton = 'إنشاء حساب';
  static const String saveButton = 'حفظ';
  static const String cancelButton = 'إلغاء';
  static const String deleteButton = 'حذف';
  static const String editButton = 'تعديل';
  static const String shareButton = 'مشاركة';
  static const String contactButton = 'تواصل';
  static const String addToFavoritesButton = 'إضافة للمفضلة';
  static const String removeFromFavoritesButton = 'إزالة من المفضلة';
  
  // Navigation Labels
  static const String homeLabel = 'الرئيسية';
  static const String searchLabel = 'البحث';
  static const String favoritesLabel = 'المفضلة';
  static const String chatLabel = 'الرسائل';
  static const String profileLabel = 'الملف الشخصي';
  
  // URLs
  static const String privacyPolicyUrl = 'https://usale.com/privacy';
  static const String termsOfServiceUrl = 'https://usale.com/terms';
  static const String supportUrl = 'https://usale.com/support';
  static const String aboutUrl = 'https://usale.com/about';
  
  // Social Media
  static const String facebookUrl = 'https://facebook.com/usale';
  static const String twitterUrl = 'https://twitter.com/usale';
  static const String instagramUrl = 'https://instagram.com/usale';
  static const String linkedinUrl = 'https://linkedin.com/company/usale';
  
  // File Extensions
  static const List<String> imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  static const List<String> videoExtensions = ['mp4', 'mov', 'avi', 'mkv'];
  
  // Default Values
  static const String defaultProfileImage = 'assets/images/default_profile.png';
  static const String defaultProductImage = 'assets/images/default_product.png';
  static const String noImagePlaceholder = 'assets/images/no_image.png';
}
