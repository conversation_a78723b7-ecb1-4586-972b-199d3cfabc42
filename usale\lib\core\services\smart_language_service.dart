import 'package:flutter/material.dart';
import 'dart:ui' as ui;

/// خدمة اللغة الذكية
class SmartLanguageService extends ChangeNotifier {
  static final SmartLanguageService _instance = SmartLanguageService._internal();
  factory SmartLanguageService() => _instance;
  SmartLanguageService._internal();

  // اللغة الحالية
  Locale _currentLocale = const Locale('ar', 'SA');
  bool _isAutoDetectEnabled = true;
  bool _isRTL = true;

  // اللغات المدعومة
  static const List<Locale> supportedLocales = [
    Locale('ar', 'SA'), // العربية - السعودية
    Locale('ar', 'AE'), // العربية - الإمارات
    Locale('ar', 'KW'), // العربية - الكويت
    Locale('ar', 'QA'), // العربية - قطر
    Locale('ar', 'BH'), // العربية - البحرين
    Locale('ar', 'OM'), // العربية - عمان
    Locale('en', 'US'), // الإنجليزية
    Locale('en', 'GB'), // الإنجليزية البريطانية
  ];

  // أسماء اللغات
  static const Map<String, String> languageNames = {
    'ar_SA': 'العربية (السعودية)',
    'ar_AE': 'العربية (الإمارات)',
    'ar_KW': 'العربية (الكويت)',
    'ar_QA': 'العربية (قطر)',
    'ar_BH': 'العربية (البحرين)',
    'ar_OM': 'العربية (عمان)',
    'en_US': 'English (US)',
    'en_GB': 'English (UK)',
  };

  // Getters
  Locale get currentLocale => _currentLocale;
  bool get isAutoDetectEnabled => _isAutoDetectEnabled;
  bool get isRTL => _isRTL;
  String get languageCode => _currentLocale.languageCode;
  String get countryCode => _currentLocale.countryCode ?? '';

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadSettings();
    if (_isAutoDetectEnabled) {
      _detectSystemLanguage();
    }
    _updateRTL();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    // هنا يمكن تحميل الإعدادات من SharedPreferences
    // للبساطة، سنستخدم القيم الافتراضية
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    // هنا يمكن حفظ الإعدادات في SharedPreferences
  }

  /// كشف لغة النظام تلقائياً
  void _detectSystemLanguage() {
    final systemLocale = ui.PlatformDispatcher.instance.locale;
    
    // البحث عن لغة مطابقة
    for (final locale in supportedLocales) {
      if (locale.languageCode == systemLocale.languageCode) {
        if (locale.countryCode == systemLocale.countryCode) {
          // مطابقة كاملة
          _currentLocale = locale;
          break;
        } else if (_currentLocale.languageCode != systemLocale.languageCode) {
          // مطابقة اللغة فقط
          _currentLocale = locale;
        }
      }
    }
    
    _updateRTL();
    notifyListeners();
  }

  /// تحديث اتجاه النص
  void _updateRTL() {
    _isRTL = _currentLocale.languageCode == 'ar';
  }

  /// تغيير اللغة
  void changeLanguage(Locale locale) {
    if (supportedLocales.contains(locale)) {
      _currentLocale = locale;
      _isAutoDetectEnabled = false;
      _updateRTL();
      _saveSettings();
      notifyListeners();
    }
  }

  /// تفعيل/إلغاء تفعيل الكشف التلقائي
  void toggleAutoDetect(bool enabled) {
    _isAutoDetectEnabled = enabled;
    if (enabled) {
      _detectSystemLanguage();
    }
    _saveSettings();
    notifyListeners();
  }

  /// الحصول على اسم اللغة
  String getLanguageName(Locale locale) {
    final key = '${locale.languageCode}_${locale.countryCode}';
    return languageNames[key] ?? locale.toString();
  }

  /// الحصول على اسم اللغة الحالية
  String get currentLanguageName => getLanguageName(_currentLocale);

  /// التحقق من كون اللغة عربية
  bool get isArabic => _currentLocale.languageCode == 'ar';

  /// التحقق من كون اللغة إنجليزية
  bool get isEnglish => _currentLocale.languageCode == 'en';

  /// الحصول على النصوص المترجمة
  String getText(String key) {
    return _translations[_currentLocale.toString()]?[key] ?? 
           _translations['ar_SA']?[key] ?? 
           key;
  }

  /// قاموس الترجمات
  static const Map<String, Map<String, String>> _translations = {
    'ar_SA': {
      'app_name': 'يوسيل',
      'app_description': 'السوق الإلكتروني الأول في الخليج',
      'home': 'الرئيسية',
      'search': 'البحث',
      'favorites': 'المفضلة',
      'profile': 'الحساب',
      'add_product': 'انشر إعلان',
      'categories': 'الفئات الرئيسية',
      'welcome': 'مرحباً بك في يوسيل',
      'discover': 'اكتشف أفضل المنتجات والعروض',
      'notifications': 'الإشعارات',
      'settings': 'الإعدادات',
      'dark_mode': 'الوضع الليلي',
      'language': 'اللغة',
      'auto_detect': 'كشف تلقائي',
      'contractors': 'مقاولات',
      'electronics': 'إلكترونيات',
      'real_estate': 'عقارات',
      'vehicles': 'مركبات',
      'fashion_family': 'أزياء و أسرة',
      'animals': 'حيوانات',
      'camping': 'تخييم',
      'services': 'خدمات',
      'miscellaneous': 'أغراض متنوعة',
      'jobs': 'وظائف',
      'furniture': 'أثاث',
      'gifts': 'هدايا',
      'search_products': 'ابحث عن المنتجات...',
      'no_favorites': 'لا توجد مفضلات بعد',
      'app_working': 'التطبيق يعمل بنجاح!',
      'all_features_ready': 'جميع الميزات متاحة وجاهزة للاستخدام',
    },
    'en_US': {
      'app_name': 'USale',
      'app_description': 'The First Electronic Market in the Gulf',
      'home': 'Home',
      'search': 'Search',
      'favorites': 'Favorites',
      'profile': 'Profile',
      'add_product': 'Post Ad',
      'categories': 'Main Categories',
      'welcome': 'Welcome to USale',
      'discover': 'Discover the best products and offers',
      'notifications': 'Notifications',
      'settings': 'Settings',
      'dark_mode': 'Dark Mode',
      'language': 'Language',
      'auto_detect': 'Auto Detect',
      'contractors': 'Contractors',
      'electronics': 'Electronics',
      'real_estate': 'Real Estate',
      'vehicles': 'Vehicles',
      'fashion_family': 'Fashion & Family',
      'animals': 'Animals',
      'camping': 'Camping',
      'services': 'Services',
      'miscellaneous': 'Miscellaneous',
      'jobs': 'Jobs',
      'furniture': 'Furniture',
      'gifts': 'Gifts',
      'search_products': 'Search for products...',
      'no_favorites': 'No favorites yet',
      'app_working': 'App is working successfully!',
      'all_features_ready': 'All features are available and ready to use',
    },
  };

  /// الحصول على اتجاه النص
  TextDirection get textDirection => _isRTL ? TextDirection.rtl : TextDirection.ltr;

  /// الحصول على محاذاة النص
  TextAlign get textAlign => _isRTL ? TextAlign.right : TextAlign.left;

  /// الحصول على محاذاة النص المعاكسة
  TextAlign get oppositeTextAlign => _isRTL ? TextAlign.left : TextAlign.right;

  /// الحصول على MainAxisAlignment للـ RTL
  MainAxisAlignment get mainAxisAlignment => 
    _isRTL ? MainAxisAlignment.end : MainAxisAlignment.start;

  /// الحصول على CrossAxisAlignment للـ RTL
  CrossAxisAlignment get crossAxisAlignment => 
    _isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start;

  /// الحصول على EdgeInsets للـ RTL
  EdgeInsets getRTLPadding({
    double top = 0,
    double bottom = 0,
    double start = 0,
    double end = 0,
  }) {
    return _isRTL 
      ? EdgeInsets.only(top: top, bottom: bottom, right: start, left: end)
      : EdgeInsets.only(top: top, bottom: bottom, left: start, right: end);
  }

  /// الحصول على BorderRadius للـ RTL
  BorderRadius getRTLBorderRadius({
    double topStart = 0,
    double topEnd = 0,
    double bottomStart = 0,
    double bottomEnd = 0,
  }) {
    return _isRTL 
      ? BorderRadius.only(
          topRight: Radius.circular(topStart),
          topLeft: Radius.circular(topEnd),
          bottomRight: Radius.circular(bottomStart),
          bottomLeft: Radius.circular(bottomEnd),
        )
      : BorderRadius.only(
          topLeft: Radius.circular(topStart),
          topRight: Radius.circular(topEnd),
          bottomLeft: Radius.circular(bottomStart),
          bottomRight: Radius.circular(bottomEnd),
        );
  }
}

/// امتداد للحصول على النصوص المترجمة
extension SmartTranslation on BuildContext {
  String tr(String key) {
    return SmartLanguageService().getText(key);
  }
  
  bool get isRTL => SmartLanguageService().isRTL;
  TextDirection get textDirection => SmartLanguageService().textDirection;
  TextAlign get textAlign => SmartLanguageService().textAlign;
}
