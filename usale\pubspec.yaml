name: usale
description: "تطبيق السوق الإلكتروني المتكامل - USale"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # State Management
  provider: ^6.1.2
  get: ^4.6.6

  # Navigation
  go_router: ^14.2.7

  # Storage & Database
  shared_preferences: ^2.2.3
  sqflite: ^2.3.3+1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path: ^1.8.3

  # Network & API
  http: ^1.2.2
  dio: ^5.4.3+1
  connectivity_plus: ^6.0.5

  # Authentication & Security
  firebase_core: ^2.0.0
  firebase_auth: ^4.0.0
  cloud_firestore: ^4.0.0
  firebase_storage: ^11.0.0
  firebase_messaging: ^14.0.0
  flutter_local_notifications: ^17.2.3
  google_sign_in: ^6.2.1
  crypto: ^3.0.3

  # Phone Authentication
  country_code_picker: ^3.0.0
  pin_code_fields: ^8.0.1

  # Image & Media
  image_picker: ^1.1.2
  photo_view: ^0.15.0
  video_player: ^2.9.1

  # Location & Maps
  geolocator: ^12.0.0
  geocoding: ^3.0.0
  google_maps_flutter: ^2.9.0

  # Utilities
  intl: ^0.20.2

  # Serialization
  json_annotation: ^4.8.1
  url_launcher: ^6.3.0
  share_plus: ^10.0.2
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # Chat & Communication
  socket_io_client: ^2.0.3+1

  # Animations
  flutter_staggered_animations: ^1.1.1
  animate_do: ^3.3.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

  # App icon generation
  flutter_launcher_icons: ^0.13.1

# Flutter Launcher Icons configuration
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/app_icon.png"
    background_color: "#2DD4BF"
    theme_color: "#2DD4BF"
  windows:
    generate: true
    image_path: "assets/images/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/app_icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
