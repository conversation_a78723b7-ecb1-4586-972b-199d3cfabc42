import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/home_app_bar.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/categories_section.dart';
import '../widgets/featured_products_section.dart';
import '../widgets/recent_products_section.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 100 && !_isScrolled) {
      setState(() {
        _isScrolled = true;
      });
    } else if (_scrollController.offset <= 100 && _isScrolled) {
      setState(() {
        _isScrolled = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar
          HomeAppBar(isScrolled: _isScrolled),
          
          // Search Bar
          const SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: SearchBarWidget(),
            ),
          ),
          
          // Categories Section
          const SliverToBoxAdapter(
            child: CategoriesSection(),
          ),
          
          // Featured Products Section
          const SliverToBoxAdapter(
            child: FeaturedProductsSection(),
          ),
          
          // Recent Products Section
          const SliverToBoxAdapter(
            child: RecentProductsSection(),
          ),
          
          // Bottom Spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
      floatingActionButton: AnimatedScale(
        scale: _isScrolled ? 1.0 : 0.0,
        duration: AppConstants.shortAnimation,
        child: FloatingActionButton(
          onPressed: () {
            _scrollController.animateTo(
              0,
              duration: AppConstants.mediumAnimation,
              curve: Curves.easeInOut,
            );
          },
          backgroundColor: AppColors.primary,
          child: const Icon(
            Icons.keyboard_arrow_up,
            color: AppColors.white,
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
