enum PaymentMethodType {
  creditCard,
  debitCard,
  applePay,
  googlePay,
  stcPay,
  mada,
  cashOnDelivery,
  bankTransfer,
  wallet,
}

class PaymentMethodModel {
  final String id;
  final PaymentMethodType type;
  final String displayName;
  final String? cardNumber; // Last 4 digits for cards
  final String? expiryDate;
  final String? cardHolderName;
  final String? bankName;
  final String? walletId;
  final bool isDefault;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  PaymentMethodModel({
    required this.id,
    required this.type,
    required this.displayName,
    this.cardNumber,
    this.expiryDate,
    this.cardHolderName,
    this.bankName,
    this.walletId,
    required this.isDefault,
    this.createdAt,
    this.updatedAt,
  });

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'],
      type: PaymentMethodType.values.firstWhere(
        (e) => e.toString() == 'PaymentMethodType.${json['type']}',
        orElse: () => PaymentMethodType.creditCard,
      ),
      displayName: json['displayName'],
      cardNumber: json['cardNumber'],
      expiryDate: json['expiryDate'],
      cardHolderName: json['cardHolderName'],
      bankName: json['bankName'],
      walletId: json['walletId'],
      isDefault: json['isDefault'] ?? false,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'displayName': displayName,
      'cardNumber': cardNumber,
      'expiryDate': expiryDate,
      'cardHolderName': cardHolderName,
      'bankName': bankName,
      'walletId': walletId,
      'isDefault': isDefault,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  PaymentMethodModel copyWith({
    String? id,
    PaymentMethodType? type,
    String? displayName,
    String? cardNumber,
    String? expiryDate,
    String? cardHolderName,
    String? bankName,
    String? walletId,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethodModel(
      id: id ?? this.id,
      type: type ?? this.type,
      displayName: displayName ?? this.displayName,
      cardNumber: cardNumber ?? this.cardNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      cardHolderName: cardHolderName ?? this.cardHolderName,
      bankName: bankName ?? this.bankName,
      walletId: walletId ?? this.walletId,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get typeDisplayName {
    switch (type) {
      case PaymentMethodType.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethodType.debitCard:
        return 'بطاقة خصم';
      case PaymentMethodType.applePay:
        return 'Apple Pay';
      case PaymentMethodType.googlePay:
        return 'Google Pay';
      case PaymentMethodType.stcPay:
        return 'STC Pay';
      case PaymentMethodType.mada:
        return 'مدى';
      case PaymentMethodType.cashOnDelivery:
        return 'الدفع عند الاستلام';
      case PaymentMethodType.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethodType.wallet:
        return 'محفظة رقمية';
    }
  }

  String get iconPath {
    switch (type) {
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        return 'assets/icons/credit_card.png';
      case PaymentMethodType.applePay:
        return 'assets/icons/apple_pay.png';
      case PaymentMethodType.googlePay:
        return 'assets/icons/google_pay.png';
      case PaymentMethodType.stcPay:
        return 'assets/icons/stc_pay.png';
      case PaymentMethodType.mada:
        return 'assets/icons/mada.png';
      case PaymentMethodType.cashOnDelivery:
        return 'assets/icons/cash.png';
      case PaymentMethodType.bankTransfer:
        return 'assets/icons/bank.png';
      case PaymentMethodType.wallet:
        return 'assets/icons/wallet.png';
    }
  }

  bool get requiresCardDetails {
    return type == PaymentMethodType.creditCard || 
           type == PaymentMethodType.debitCard ||
           type == PaymentMethodType.mada;
  }

  bool get isDigitalWallet {
    return type == PaymentMethodType.applePay ||
           type == PaymentMethodType.googlePay ||
           type == PaymentMethodType.stcPay ||
           type == PaymentMethodType.wallet;
  }

  String get maskedCardNumber {
    if (cardNumber == null || cardNumber!.length < 4) {
      return '****';
    }
    return '**** **** **** ${cardNumber!.substring(cardNumber!.length - 4)}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethodModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PaymentMethodModel(id: $id, type: $type, displayName: $displayName)';
  }
}

class PaymentTransaction {
  final String id;
  final String orderId;
  final String userId;
  final double amount;
  final PaymentMethodModel paymentMethod;
  final PaymentStatus status;
  final String? transactionReference;
  final String? failureReason;
  final DateTime createdAt;
  final DateTime? completedAt;

  PaymentTransaction({
    required this.id,
    required this.orderId,
    required this.userId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    this.transactionReference,
    this.failureReason,
    required this.createdAt,
    this.completedAt,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id'],
      orderId: json['orderId'],
      userId: json['userId'],
      amount: json['amount'].toDouble(),
      paymentMethod: PaymentMethodModel.fromJson(json['paymentMethod']),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == 'PaymentStatus.${json['status']}',
        orElse: () => PaymentStatus.pending,
      ),
      transactionReference: json['transactionReference'],
      failureReason: json['failureReason'],
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderId': orderId,
      'userId': userId,
      'amount': amount,
      'paymentMethod': paymentMethod.toJson(),
      'status': status.toString().split('.').last,
      'transactionReference': transactionReference,
      'failureReason': failureReason,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'في الانتظار';
      case PaymentStatus.processing:
        return 'قيد المعالجة';
      case PaymentStatus.completed:
        return 'مكتمل';
      case PaymentStatus.failed:
        return 'فشل';
      case PaymentStatus.cancelled:
        return 'ملغي';
      case PaymentStatus.refunded:
        return 'مسترد';
    }
  }

  bool get isSuccessful => this == PaymentStatus.completed;
  bool get isFinal => this == PaymentStatus.completed || 
                     this == PaymentStatus.failed || 
                     this == PaymentStatus.cancelled ||
                     this == PaymentStatus.refunded;
}
